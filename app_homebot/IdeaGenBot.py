from io import Bytes<PERSON>
from typing import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>
from google import genai
from google.genai import types
from PIL import Image

from app_dxf.DXFAgent import LLMAgent, SimpleResponse
from common.GlobalConstants import GlobalConstants


class IdeaGenBot:

    client = genai.Client(
        api_key="AIzaSyBKvXjiciwV8UlOMiUNawoRZaRZY7xjbFY"
    )  # <EMAIL>
    text_input = (
        "This is a picture of a kitchen."
        "Can you style it like a modern kitchen, it should have minimalistic design"
    )
    llmAgent = LLMAgent(api_key=GlobalConstants.OPENAI_API_KEY)

    @classmethod
    async def generate_idea(
        cls,
        input_image: BinaryIO,
        theme_image: BinaryIO,
        text_input: str,
        candidate_count: int = 1,
    ) -> Tuple[List[BinaryIO], str, str]:

        input_image.seek(0)
        image = Image.open(input_image)
        theme_prompt = ""
        if theme_image is not None:
            print("Theme image is not None")
            prompt_response: SimpleResponse = await IdeaGenBot.llmAgent.call_llm(
                system_prompt="You are an expert Prompt Engineer. You can look at images and generate prompts for them. You output only the prompt and nothing else.",
                user_prompt="Here is an image of an architectural space theme form pinterest. Generate a prompt for it, which can be used to generate a similar themed high quality 4k image for a different space. Return only the prompt and nothing else.",
                image_buffers=[theme_image],
            )
            print("Prompt response, updating input promot", prompt_response)
            text_input = text_input.replace(
                "__theme_image_prompt__", prompt_response.response
            )
            print("Updated text input", text_input)
            theme_prompt = prompt_response.response
        response = IdeaGenBot.client.models.generate_content(
            model="gemini-2.0-flash-exp-image-generation",
            contents=[text_input, image],
            config=types.GenerateContentConfig(
                response_modalities=["Text", "Image"], candidate_count=candidate_count
            ),
        )
        output_images = []
        for part in response.candidates[0].content.parts:
            if part.text is not None:
                print(part.text)
            elif part.inline_data is not None:
                output_images.append(BytesIO(part.inline_data.data))

        print("output_images", len(output_images))
        return output_images, text_input, theme_prompt
