from fastapi import APIRouter
from typing import List, Optional
from fastapi import APIRouter, FastAPI, File, Form, Header, UploadFile
from pydantic import BaseModel, Field

from app_homebot.IdeaGenBot import IdeaGenBot
from common import httputils
from common.commonmodels import Request<PERSON>ontext
from common.models.common_response_models import StringResposne


class HomeBotHttp:
    def __init__(self):
        self.router = APIRouter(prefix="/homebot")

        @self.router.post("/image_idea_gen")
        async def image_idea_gen_api(
            header_request_context: str = Header(...),
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
        ):
            print("Loading input", input_json)
            print("Files", files)
            print("header_request_context", header_request_context)
            """
            request_ctx_obj = RequestContext.model_validate_json(header_request_context)
            print(
                "request_ctx_obj",
                request_ctx_obj,
                request_ctx_obj.clientVersion,
                request_ctx_obj.tokenExpiry,
            )
            """
            theme_image = None
            if files.__len__() > 1:
                print("More than 1 file, assuming a theme image")

                theme_image = files[1].file

            images, input_prompt_used, theme_prompt_generated = await IdeaGenBot.generate_idea(
                files[0].file, theme_image, input_json
            )

            files_to_return = []
            cntr: int = 0
            for img in images:
                img.seek(0)
                files_to_return.append(
                    UploadFile(file=img, filename=f"image_{cntr}.png")
                )

            response_json = StringResposne(
                message=f"Image idea generated successfully {cntr+1} input_json:\n {input_json} \n\ninput_prompt_used:\n {input_prompt_used} \n\ntheme_prompt_generated:\n {theme_prompt_generated}",
            )

            return httputils.create_strean_response_json_file(
                response_json, files_to_return
            )
