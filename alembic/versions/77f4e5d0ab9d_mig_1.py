"""mig 1

Revision ID: 77f4e5d0ab9d
Revises: 
Create Date: 2025-03-28 12:36:31.044651

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '77f4e5d0ab9d'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('builds',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.String(), nullable=False),
    sa.Column('project_name', sa.String(), nullable=False),
    sa.Column('build_number', sa.Integer(), nullable=True),
    sa.Column('build_status', sa.String(), nullable=False),
    sa.Column('build_url', sa.String(), nullable=True),
    sa.Column('build_queue_id', sa.Integer(), nullable=False),
    sa.Column('build_created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_builds_account_id'), 'builds', ['account_id'], unique=False)
    op.create_index(op.f('ix_builds_build_created_at'), 'builds', ['build_created_at'], unique=False)
    op.create_index(op.f('ix_builds_id'), 'builds', ['id'], unique=False)
    op.create_index(op.f('ix_builds_project_name'), 'builds', ['project_name'], unique=False)
    op.create_table('users',
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('password', sa.String(), nullable=True),
    sa.Column('account_id', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('access_token', sa.String(), nullable=True),
    sa.Column('token_expiry', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('email')
    )
    op.create_index(op.f('ix_users_account_id'), 'users', ['account_id'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_account_id'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_builds_project_name'), table_name='builds')
    op.drop_index(op.f('ix_builds_id'), table_name='builds')
    op.drop_index(op.f('ix_builds_build_created_at'), table_name='builds')
    op.drop_index(op.f('ix_builds_account_id'), table_name='builds')
    op.drop_table('builds')
    # ### end Alembic commands ###
