"""mig 3 - bake and furniture in build

Revision ID: c95395064405
Revises: 1a37a2f3fcca
Create Date: 2025-06-11 08:37:46.737156

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c95395064405'
down_revision: Union[str, None] = '1a37a2f3fcca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('builds', sa.Column('bake_level', sa.Integer(), nullable=True))
    op.add_column('builds', sa.Column('furniture_toggle', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('builds', 'furniture_toggle')
    op.drop_column('builds', 'bake_level')
    # ### end Alembic commands ###
