"""mig 2 -build requested by col

Revision ID: 1a37a2f3fcca
Revises: 77f4e5d0ab9d
Create Date: 2025-04-10 10:00:05.363135

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a37a2f3fcca'
down_revision: Union[str, None] = '77f4e5d0ab9d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('builds', sa.Column('requested_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('builds', 'requested_by')
    # ### end Alembic commands ###
