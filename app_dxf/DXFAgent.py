import asyncio
import re
from typing import Binary<PERSON>, ClassVar, List, Union
from dotenv import load_dotenv
import os
from openai import AsyncOpenAI
from pydantic import BaseModel
from pydantic import BaseModel, Field
from typing import List, Type, Dict
from enum import Enum, auto
from typing import TypeVar, Type, Generic
import json
import base64
from ezdxf.document import Drawing

T = TypeVar("T", bound=BaseModel)


class SimpleResponse(BaseModel):
    response: str = Field(description="response to the user prompt")


# Function to encode the image
def encode_image(image_buffer: BinaryIO):
    image_buffer.seek(0)
    return base64.b64encode(image_buffer.read()).decode("utf-8")


image_base_64_cache: dict[str, str] = {}


def base64_encode_image_url(image_buffer: BinaryIO):
    # png is ok?
    # or jpeg?
    return f"data:image/png;base64,{encode_image(image_buffer)}"


T = TypeVar("T")


class MuscleMemory(Generic[T]):

    mem: dict[str, T] = {}

    def is_present_in_memory(self, input: str) -> bool:
        return input in self.mem

    def get_from_muscle_mem(self, input: str) -> T | None:
        return self.mem.get(input)

    def add_to_muscle_memory(self, input: str, output: T):
        self.mem[input] = output

    def add_to_multi_input_muscle_memory(self, output: T, input: List[str]):
        for inp in input:
            self.mem[inp] = output

    def format_mem_as_KV_lines(self):
        formatted_lines = [f"{key} : {value}" for key, value in self.mem.items()]
        return "\n".join(formatted_lines)


class LLMAgent:

    def __init__(self, api_key: str | None = None):
        if api_key is None:
            load_dotenv()
            api_key = os.getenv("OPENAI_API_KEY")
        self.client = AsyncOpenAI(api_key=api_key)

    async def call_llm(
        self,
        system_prompt: str,
        user_prompt: str,
        image_buffers: List[BinaryIO] = [],
        response_format_model: Type[T] = SimpleResponse,
    ) -> T:
        messages_obj = []
        messages_obj.append({"role": "system", "content": system_prompt})
        user_prompt_content = []
        user_prompt_content.append({"type": "text", "text": user_prompt})
        if image_buffers and len(image_buffers) > 0:
            for img_buffer in image_buffers:
                user_prompt_content.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": base64_encode_image_url(img_buffer),
                            "detail": "high",
                        },
                    }
                )

        messages_obj.append({"role": "user", "content": user_prompt_content})

        response = await self.client.beta.chat.completions.parse(
            # model="gpt-4o-2024-08-06",
            model="gpt-4o",
            #model="o1",
            #model="gpt-4o-mini-2024-07-18",
            messages=messages_obj,
            max_tokens=1000,
            temperature=0.7,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            response_format=response_format_model,
        )

        # print(json.dumps(messages_obj, indent=2))

        if response.choices[0].message.parsed is None:
            raise ValueError("Response parsing failed")

        if True:
            print(
                f"""
            ------\n
            Sys Prompt: {system_prompt}
            User PROMPT: {user_prompt}
            EXPECTED TYPE: {response_format_model.__name__}
            RESPONSE: {response.choices[0].message.parsed} 
            ------\n
            """
            )

        return response.choices[0].message.parsed


class WindowConfigData(BaseModel):
    name: str = Field()
    height: float = Field()
    sill_height: float = Field()


class DoorConfigData(BaseModel):
    name: str = Field()
    height: float = Field()


class WindowConfigListResponse(BaseModel):
    window_config_list: List[WindowConfigData] = Field(
        default_factory=list, description="List of window configurations"
    )


class DoorConfigListResponse(BaseModel):
    door_config_list: List[DoorConfigData] = Field(
        default_factory=list, description="List of door configurations"
    )


class WallLayersResponse(BaseModel):
    wall_layers: List[str] = Field(
        default_factory=list, description="List of wall layers, empty if none"
    )


class Category(str, Enum):
    WINDOW_SYMBOL = "WINDOW_SYMBOL"
    DOOR_SYMBOL = "DOOR_SYMBOL"
    VENT_SYMBOL = "VENT_SYMBOL"
    AREA_NAME = "AREA_NAME"
    UNIT_NAME = "UNIT_NAME"
    STRUCTURE_TYPE = "STRUCTURE_TYPE"
    DIMENSION_DETAILS = "DIMENSION_DETAILS"
    FURNITURE = "FURNITURE"
    UNIDENTIFIED = "UNIDENTIFIED"


class TextLabelCategoryPair(BaseModel):
    text: str = Field(..., description="Input Text Label")
    category: Category = Field(
        default=Category.UNIDENTIFIED,
        description="Text category",
    )


class TextLabel_Category_Response(BaseModel):
    text_label_category_list: List[TextLabelCategoryPair] = Field(
        ...,
        description="list of text label and its category objects",
    )


class FloorPlanImageResponse(BaseModel):
    architect_name: str = Field(description="Architect name")
    architect_company: str = Field(description="Architect company")
    project_name: str = Field(description="Project name")
    project_description: str = Field(description="Project description")
    project_location: str = Field(description="Project location")
    client_name: str = Field(description="Client name")
    floor_number: int = Field(description="Floor number of the plan")
    floor_level_height: float = Field(description="Floor level height")
    ceiling_level_height: float = Field(description="Ceiling level height")
    door_symbols: List[str] = Field(
        description="Door Names in the image displayed near Door Symbols of an architecural floor plan . E.g : D1, MD, SD, etc."
    )
    window_symbols: List[str] = Field(
        description="Window Names in the image displayed near Window Symbols of an architecural floor plan . E.g : W1, SW etc."
    )


class PredictionCache(BaseModel):
    cache_file: ClassVar[str] = "predictioncache.json"

    # known regex patterns for wall layers
    LAYER_WALL_PATTERNS: ClassVar[list[str]] = [
        r".*(\b|_)WALL(\b|_).*",
        r".*(\b|_)WALLS(\b|_).*",
        r".*(\b|_)STRUC(\b|_).*",
        r".*(\b|_)ELEV(\b|_).*",
        r".*(\b|_)BRICK(\b|_).*",
        r".*(\b|_)SLAB(\b|_).*",
        r".*(\b|_)CONC(\b|_).*",
        r".*(\b|_)REIN(\b|_).*",
        r".*(\b|_)REINFORC(\b|_).*",
        r".*(\b|_)REINFORCE(\b|_).*",
    ]
    wall_layers: set[str] = Field(
        default_factory=set, description="List of wall layers, empty if none"
    )
    non_wall_layers: set[str] = Field(
        default_factory=set, description="List of non wall layers, empty if none"
    )

    @classmethod
    def load_cache(cls) -> "PredictionCache":
        """Load predictions from cache file if it exists"""
        try:
            if os.path.exists(PredictionCache.cache_file):
                with open(PredictionCache.cache_file, "r") as f:
                    cache_data = json.load(f)
                    return PredictionCache(**cache_data)
        except Exception as e:
            print(f"Error loading cache: {e}")
        return PredictionCache()  # Return empty cache if loading fails

    def is_known_wall_layer(self, layer_name: str) -> bool:
        return (
            any(
                re.search(pattern, layer_name, re.IGNORECASE)
                for pattern in self.LAYER_WALL_PATTERNS
            )
            or layer_name in self.wall_layers
        )

    def is_known_non_wall_layer(self, layer_name: str) -> bool:
        return layer_name in self.non_wall_layers

    def mark_known_wall_layer_names(self, wall_layers: set[str]):
        for layer in wall_layers:
            if layer not in self.wall_layers:
                self.wall_layers.add(layer)

    def mark_non_wall_layer_name(self, non_wall_layer: str):
        self.non_wall_layers.add(non_wall_layer)

    def model_dump(self, **kwargs):
        # Convert sets to lists for JSON serialization
        data = super().model_dump(**kwargs)
        data["wall_layers"] = list(data["wall_layers"])
        data["non_wall_layers"] = list(data["non_wall_layers"])
        return data

    def save_cache(self):
        """Save current predictions to cache file"""
        try:
            cache_data = self.model_dump()
            with open(self.cache_file, "w") as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            print(f"Error saving cache: {e}")


class DXFAgent:

    def __init__(self, doc: Drawing, api_key: str | None = None):
        self.doc = doc
        self.llm_agent = LLMAgent(api_key)
        self.wall_layers: set[str] = set()

        self.prediction_cache = PredictionCache.load_cache()
        self.musc_mem_text_cats = MuscleMemory[Category]()
        self.musc_mem_text_cats.add_to_multi_input_muscle_memory(
            Category.WINDOW_SYMBOL,
            ["W", "W1", "W2", "W3", "W4", "W5", "W6", "W7", "W8", "W9"],
        )
        self.musc_mem_text_cats.add_to_multi_input_muscle_memory(
            Category.WINDOW_SYMBOL,
            ["SW", "SW1", "SW2", "SW3", "SW4", "SW5", "SW6", "SW7", "SW8", "SW9"],
        )

        self.musc_mem_text_cats.add_to_multi_input_muscle_memory(
            Category.DOOR_SYMBOL,
            [
                "D",
                "MD",
                "D1",
                "D2",
                "D3",
                "D4",
                "D5",
                "D6",
                "D7",
                "D8",
                "D9",
                "MD1",
                "MD2",
            ],
        )

    async def get_wall_layers(self):
        unknown_layer_names: set = set()

        for layer in self.doc.layers:
            if self.prediction_cache.is_known_wall_layer(layer_name=layer.dxf.name):
                self.wall_layers.add(layer.dxf.name)
            else:
                if not self.prediction_cache.is_known_non_wall_layer(
                    layer_name=layer.dxf.name
                ):
                    unknown_layer_names.add(layer.dxf.name)

        print("cache based wall_layers ", self.wall_layers)
        if len(unknown_layer_names) > 0:
            wall_layers_response = await self.llm_agent.call_llm(
                system_prompt="You are a helpful assistant that identifies wall layers in a floor plan DWG file.",
                user_prompt=f"Here are the layers in the DWG file: {unknown_layer_names}. Return only list of WALL Layers - that you are SURE are walls. Stairs is not a wall. If nothing, don't return anything.",
                response_format_model=WallLayersResponse,
            )
            self.wall_layers.update(wall_layers_response.wall_layers)

        print("unknown_layer_names ", unknown_layer_names)
        print("final all_wall_layers ", self.wall_layers)

        self.prediction_cache.mark_known_wall_layer_names(self.wall_layers)
        for layer in unknown_layer_names:
            if layer not in wall_layers_response.wall_layers:
                self.prediction_cache.mark_non_wall_layer_name(layer)

        self.prediction_cache.save_cache()
        return self.wall_layers

    async def get_floor_plan_image_response(
        self, img_buffer: BinaryIO
    ) -> FloorPlanImageResponse:
        response: FloorPlanImageResponse = await self.llm_agent.call_llm(
            system_prompt="You are an Architect who can understand a Floor plan. Return only the information that is present in the floor plan and you are SURE about. If the required info is not present, return NA or -1",
            user_prompt="Get all relevent details from this floor plan",
            image_buffers=[img_buffer],
            response_format_model=FloorPlanImageResponse,
        )
        print("llm response for get_floor_plan_image_response", response)
        return response

    async def get_door_config_list(
        self, door_keys: set[str], img_buffer: BinaryIO
    ) -> DoorConfigListResponse:
        response: DoorConfigListResponse = await self.llm_agent.call_llm(
            system_prompt="You are an Architect who can understand a Floor plan image. Return only the information that is present in the floor plan and you are SURE about. You are needed to return the door config for the door keys provided. If the required info is not present, then don't return that specific door key",
            user_prompt=f"Door keys: {door_keys}. Look at the given image, and return the door config for what ever you find.",
            image_buffers=[img_buffer],
            response_format_model=DoorConfigListResponse,
        )
        return response

    async def get_window_config_list(
        self, window_keys: set[str], img_buffer: BinaryIO
    ) -> WindowConfigListResponse:
        response: WindowConfigListResponse = await self.llm_agent.call_llm(
            system_prompt="You are an Architect who can understand a Floor plan image. Return only the information that is present in the floor plan and you are SURE about. You are needed to return the window config for the window keys provided. If the required info is not present, then don't return that specific window key",
            user_prompt=f"Window keys: {window_keys}. Look at the given image, and return the window config for what ever you find.",
            image_buffers=[img_buffer],
            response_format_model=WindowConfigListResponse,
        )
        return response

    async def get_text_label_category(
        self, unique_text_labels: set[str]
    ) -> TextLabel_Category_Response:
        # Process text labels in batches of 10
        batch_size = 20
        text_labels_list = list(unique_text_labels)
        # Remove text labels that are already in muscle memory
        text_labels_list = [
            label
            for label in text_labels_list
            if not self.musc_mem_text_cats.is_present_in_memory(label)
        ]
        all_responses = []
        all_batch_tasks = []
        for i in range(0, len(text_labels_list), batch_size):
            print(
                f"Processing batch {i // batch_size + 1} of {len(text_labels_list) // batch_size}"
            )
            batch = {"text_labels": text_labels_list[i : i + batch_size]}

            prompt = f"""
            Return a list of input text labels with its predicted category. This the json of input text labels:
            ```json
            {json.dumps(batch, indent=2)}

            """
            # prompt = {json.dumps(batch, indent=2)}
            # print(prompt)
            all_batch_tasks.append(
                self.llm_agent.call_llm(
                    system_prompt=f"""
                    You are an Architect who can understand a Floor plan very deeply. You will look at the supplied list of Text Labels from a floor plan, and return its CATEGORY along with the input text - that closely matches. Return only what you are SURE about. If not sure, return UNIDENTIFIED
                    Possible CATEGORIES: {",".join([category.value for category in Category])}

                    Sample Training Data you can use as this :

                    {self.musc_mem_text_cats.format_mem_as_KV_lines()}

                    """,
                    user_prompt=prompt,
                    response_format_model=TextLabel_Category_Response,
                )
            )
            # print("batch_response", batch_response)

        batch_response: TextLabel_Category_Response = await asyncio.gather(
            *all_batch_tasks
        )
        for response in batch_response:
            all_responses.extend(response.text_label_category_list)
        # Combine all responses into a single TextLabel_Category_Response
        combined_response = TextLabel_Category_Response(
            text_label_category_list=all_responses
        )

        for k, v in self.musc_mem_text_cats.mem.items():
            combined_response.text_label_category_list.append(
                TextLabelCategoryPair(text=k, category=v)
            )

        return combined_response
