from typing import List, Optional
from fastapi import APIRouter, FastAPI, File, Form, Header, UploadFile

from app_dxf.DXFUtils import DXFFileProcessor
from common import httputils
from common.commonmodels import RequestContext


class DXFHttp:
    def __init__(self):
        self.router = APIRouter(prefix="/dxf")

        @self.router.post("/parse_dxf")
        async def parse_dxf(
            header_request_context: str = Header(...),
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
        ):
            print("Loading DXF", input_json)
            print("Files", files)
            print("header_request_context", header_request_context)
            request_ctx_obj = RequestContext.model_validate_json(header_request_context)
            print(
                "request_ctx_obj",
                request_ctx_obj,
                request_ctx_obj.clientVersion,
                request_ctx_obj.tokenExpiry,
            )
            response_json = {"message": "DXF parsed successfully", "input": input_json}
            dxfProcessor = DXFFileProcessor(files[0].file, request_ctx_obj.accountId)
            await dxfProcessor.process()
            response_json = dxfProcessor.dxf_data
            dxfProcessor.image_buffer.seek(0)
            files_to_return = [
                UploadFile(file=dxfProcessor.image_buffer, filename="image.png")
            ]
            return httputils.create_strean_response_json_file(
                response_json, files_to_return
            )
