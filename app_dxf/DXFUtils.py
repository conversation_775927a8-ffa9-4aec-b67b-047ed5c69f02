import argparse
import async<PERSON>
from enum import Enum, auto
import hashlib
import io
import json
import shutil
from typing import BinaryIO, Dict, List, Optional
import os
import re

from matplotlib.axes import Axes
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import matplotlib
import ezdxf

from ezdxf.addons.drawing import RenderContext, Frontend
from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
from ezdxf.document import Drawing

from ezdxf.layouts import Modelspace, Paperspace
from ezdxf.layouts.layouts import Layouts
from ezdxf.sections.blocks import BlocksSection
from ezdxf import bbox
from ezdxf.entities import factory

from ezdxf.entities import Insert, DXFEntity, DXFGraphic, Attrib
from ezdxf.math import Vec3
from ezdxf import bbox
from ezdxf import units

from PIL import Image

from pydantic import BaseModel
from pydantic import BaseModel, Field

from common.models.DictThatUnityCanUnderstand import DictThatUnityCanUnderstand
from common.storage.DiskFileCache import DiskFileCache
from ezdxf.addons.drawing.config import Configuration  # Ensure Configuration is imported

#### Local imports
from .DXFAgent import (
    DXFAgent,
    DoorConfigData,
    DoorConfigListResponse,
    FloorPlanImageResponse,
    TextLabel_Category_Response,
    Category,
    TextLabelCategoryPair,
    WindowConfigData,
    WindowConfigListResponse,
)

matplotlib.use("agg")

#### End of imports

args: argparse.Namespace = None


class Constants:
    unitsmap = {
        units.MM: "MM",
        units.CM: "CM",
        units.M: "M",
        units.KM: "KM",
        units.IN: "IN",
        units.FT: "FT",
        units.MI: "MI",
        units.YD: "YD",
        units.DM: "DM",
    }

    ## Conversion factors for Unity3D : assumes Unity3D uses meters as base unit
    UNITY3d_CONVERSION_MULTIPLIER = {
        units.MM: 0.001,
        units.CM: 0.01,
        units.M: 1,
        units.KM: 1000,
        units.IN: 0.0254,
        units.FT: 0.3048,
        units.MI: 1609.34,
        units.YD: 0.9144,
        units.DM: 0.1,
    }

    PRIMITIVE_GEOMETRIC_DXF_TYPES = [
        "LINE",
        "LWPOLYLINE",
        "POLYLINE",
        "CIRCLE",
        "ELLIPSE",
        "ARC",
        "SPLINE",
    ]

    PRIMITIVE_TEXT_DXF_TYPES = ["TEXT", "MTEXT"]


class Point2D(BaseModel):
    x: float = Field()
    y: float = Field()


class TextLabel(BaseModel):
    text_label: str = Field()
    category: Category = Field()
    location: Point2D = Field()


class DXFData(BaseModel):
    dxf_data_version: int = Field(default=4, description="Version of the DXF data")
    units: str = Field(default=None, description="Units of the DXF file")
    dxf_checksum: str = Field(default=None, description="Checksum of the DXF file")
    unity3d_conversion_multiplier: float = Field(
        default=1.0, description="Conversion factor for Unity3D"
    )

    window_config_map: DictThatUnityCanUnderstand[str, WindowConfigData] = Field(
        default_factory=lambda: DictThatUnityCanUnderstand[str, WindowConfigData](),
        description="Dictionary of window data",
    )

    door_config_map: DictThatUnityCanUnderstand[str, DoorConfigData] = Field(
        default_factory=lambda: DictThatUnityCanUnderstand[str, DoorConfigData](),
        description="Dictionary of door data",
    )

    text_labels: DictThatUnityCanUnderstand[Category, List[TextLabel]] = Field(
        default_factory=lambda: DictThatUnityCanUnderstand[Category, List[TextLabel]](),
        description="List of text labels",
    )

    wall_layers: List[str] = Field(
        default_factory=list, description="List of wall layers"
    )

    floor_plan_image_response: Optional[FloorPlanImageResponse] = Field(
        default=None, description="Floor plan image response"
    )

    image_scale_x_unity: float = Field(
        default=1.0, description="Scale in X direction of the image"
    )
    image_scale_y_unity: float = Field(
        default=1.0, description="Scale in Y direction of the image"
    )
    image_location_x_unity: float = Field(
        default=0.0, description="Location in X direction of the image"
    )
    image_location_y_unity: float = Field(
        default=0.0, description="Location in Y direction of the image"
    )

    def populate_metadata(self, doc: Drawing, text_io: io.TextIOWrapper):
        self.units = Constants.unitsmap[doc.units]
        self.unity3d_conversion_multiplier = Constants.UNITY3d_CONVERSION_MULTIPLIER[
            doc.units
        ]
        text_io.seek(0)
        self.dxf_checksum = calculate_md5_from_textio(text_io)


def calculate_md5_from_textio(text_io):
    md5 = hashlib.md5()
    # Ensure the underlying buffer is used (raw binary mode)
    binary_stream = text_io.buffer
    while chunk := binary_stream.read(4096):  # Read in binary mode
        md5.update(chunk)
    return md5.hexdigest()


class DXFFileProcessor:

    def __init__(self, dxf_file: BinaryIO, account_id: str = None):
        self.dxf_file = dxf_file
        print("Processing dxf file", dxf_file)
        self.account_id = account_id
        self.common_cache_account = "mhxp.common.cache"

    async def process(self):

        text_io = io.TextIOWrapper(self.dxf_file, encoding="utf-8")
        self.doc = ezdxf.read(text_io)
        self.msp = self.doc.modelspace()

        self.dxf_data = DXFData()

        self.dxf_data.populate_metadata(self.doc, text_io)

        cached_dxf_data_stream: io.BytesIO = await DiskFileCache.getInstance().get_file(
            self.common_cache_account,
            "dxf-data",
            self.dxf_data.dxf_checksum + "-dxf-data.json",
        )
        cached_dxf_data = None
        cached_image = None
        self.image_buffer = None
        if cached_dxf_data_stream:
            cached_dxf_data_stream.seek(0)  # Reset stream position
            try:
                cached_dxf_data = DXFData.model_validate_json(
                    cached_dxf_data_stream.read().decode("utf-8")
                )

                if cached_dxf_data.dxf_data_version < self.dxf_data.dxf_data_version:
                    print("older dxf = ", cached_dxf_data.dxf_data_version)
                    cached_dxf_data = None
            except Exception as e:
                print("Error loading cached dxf data, rebuilding", e)

        if cached_dxf_data:
            cached_image = await DiskFileCache.getInstance().get_file(
                self.common_cache_account,
                "dxf-data",
                self.dxf_data.dxf_checksum + "-dxf-image.png",
            )
            self.image_buffer = cached_image

        if self.image_buffer is None:
            print("Converting dxf to image")
            self.image_buffer = self.convert_dxf2img()

            asyncio.create_task(
                DiskFileCache.getInstance().put_file(
                    self.common_cache_account,
                    self.image_buffer,
                    "dxf-data",
                    self.dxf_data.dxf_checksum + "-dxf-image.png",
                )
            )
        if cached_dxf_data:
            print("Also returning cached dxf_data")
            self.dxf_data = cached_dxf_data
            return

        print("Processing DXF data")
        self.dxfAgent: DXFAgent = DXFAgent(
            api_key="********************************************************************************************************************************************************************",
            doc=self.doc,
        )
        process_tasks = [self.process_text_labels(), self.process_wall_layers()]
        await asyncio.gather(*process_tasks)

        # await self.process_floor_plan_image_metadata()
        # await self.process_door_window_configs()
        # await self.process_wall_layers()

        json_str: str = self.dxf_data.model_dump_json(indent=2)
        print("\nDXF Data as JSON: ", json_str)
        asyncio.create_task(
            DiskFileCache.getInstance().put_file(
                self.common_cache_account,
                io.BytesIO(json_str.encode("utf-8")),
                "dxf-data",
                self.dxf_data.dxf_checksum + "-dxf-data.json",
            )
        )
        return

    async def process_door_window_configs(self):
        door_keys = set(
            [
                text_label.text_label
                for text_label in self.dxf_data.text_labels.get(Category.DOOR_SYMBOL)
            ]
        )
        door_keys.update(self.dxf_data.floor_plan_image_response.door_symbols)

        window_keys = set(
            [
                text_label.text_label
                for text_label in self.dxf_data.text_labels.get(Category.WINDOW_SYMBOL)
            ]
        )
        window_keys.update(self.dxf_data.floor_plan_image_response.window_symbols)

        door_config_list_response: DoorConfigListResponse = (
            await self.dxfAgent.get_door_config_list(door_keys, self.image_buffer)
        )
        window_config_list_response: WindowConfigListResponse = (
            await self.dxfAgent.get_window_config_list(window_keys, self.image_buffer)
        )

        self.dxf_data.door_config_map = DictThatUnityCanUnderstand[
            str, DoorConfigData
        ]()
        for door_config in door_config_list_response.door_config_list:
            self.dxf_data.door_config_map.put(door_config.name, door_config)

        self.dxf_data.window_config_map = DictThatUnityCanUnderstand[
            str, WindowConfigData
        ]()
        for window_config in window_config_list_response.window_config_list:
            self.dxf_data.window_config_map.put(window_config.name, window_config)

    async def process_wall_layers(self):
        wall_layers = await self.dxfAgent.get_wall_layers()
        self.dxf_data.wall_layers = list(wall_layers)

    async def process_floor_plan_image_metadata(self):
        self.dxf_data.floor_plan_image_response = (
            await self.dxfAgent.get_floor_plan_image_response(self.image_buffer)
        )
        print(
            "process_floor_plan_image_metadata", self.dxf_data.floor_plan_image_response
        )

    async def process_text_labels(self):
        text_labels_from_doc: List[TextLabel] = []
        for entity in self.msp:
            self.recursive_collect_texts(entity, text_labels_from_doc)
        if False:
            print("text_labels", len(text_labels_from_doc), text_labels_from_doc)
        text_labels_set = set(
            [text_label.text_label for text_label in text_labels_from_doc]
        )
        # LLM : get unique keys - categories from
        text_label_category_response: TextLabel_Category_Response = (
            await self.dxfAgent.get_text_label_category(text_labels_set)
        )
        print(
            "text_label_category_response",
            len(text_label_category_response.text_label_category_list),
        )
        # convert list to dict
        text_labels_dict = {
            text_label_with_category.text: text_label_with_category.category
            for text_label_with_category in text_label_category_response.text_label_category_list
        }
        self.dxf_data.text_labels = DictThatUnityCanUnderstand[
            Category, List[TextLabel]
        ]()
        for text_label_from_doc in text_labels_from_doc:

            if text_label_from_doc.text_label in text_labels_dict:
                text_label_from_doc.category = text_labels_dict[
                    text_label_from_doc.text_label
                ]
            else:
                text_label_from_doc.category = Category.UNIDENTIFIED

            # if True or text_label.category != Category.UNIDENTIFIED:
            if self.dxf_data.text_labels.get(text_label_from_doc.category) is None:
                self.dxf_data.text_labels.put(text_label_from_doc.category, [])
            self.dxf_data.text_labels.get(text_label_from_doc.category).append(
                text_label_from_doc
            )

    def recursive_collect_walls(
        self, entity, walls: List[DXFGraphic], wall_layers: set[str]
    ):
        if walls is None:
            walls = []
        if entity.dxf.dxftype in Constants.PRIMITIVE_GEOMETRIC_DXF_TYPES:
            if entity.dxf.layer in wall_layers:
                if False:
                    print(f"Found wall: {entity.dxf.layer} {entity.dxf.dxftype}")
                walls.append(entity.copy())
        if entity.dxf.dxftype == "INSERT":
            insert_entity: Insert = entity
            if False:
                print(
                    f"Found blockref: {insert_entity.dxf.name} {insert_entity.dxf.insert}"
                )
            for entity in insert_entity.virtual_entities():
                self.recursive_collect_walls(entity, walls, wall_layers)
        else:
            if False:
                print(
                    f"recursive_collect_walls Skipping {entity.dxf.dxftype} {entity.dxf.layer}"
                )
            pass  # do nothing

    def recursive_collect_texts(self, entity, texts: List[TextLabel]):
        if entity.dxf.dxftype in Constants.PRIMITIVE_TEXT_DXF_TYPES:
            if entity.plain_text().strip().isdigit():
                return
            texts.append(
                TextLabel(
                    text_label=entity.plain_text().strip(),
                    category=Category.UNIDENTIFIED,
                    location=Point2D(x=entity.dxf.insert.x, y=entity.dxf.insert.y),
                )
            )
        elif entity.dxf.dxftype == "INSERT":
            insert_entity: Insert = entity
            for attrib in insert_entity.attribs:
                # https://ezdxf.readthedocs.io/en/stable/blocks/attrib.html
                attrib: Attrib = attrib
                if not attrib.is_invisible:
                    if attrib.dxf.text.strip().isdigit():
                        continue
                    texts.append(
                        TextLabel(
                            text_label=f"{attrib.dxf.text.strip()}",
                            category=Category.UNIDENTIFIED,
                            location=Point2D(
                                x=insert_entity.dxf.insert.x,
                                y=insert_entity.dxf.insert.y,
                            ),
                        )
                    )
            for entity in insert_entity.virtual_entities():
                self.recursive_collect_texts(entity, texts)
        elif entity.dxf.dxftype == "DIMENSION":
            pass
        else:
            pass  # do nothing

    def convert_dxf2img(self) -> BinaryIO:
        default_img_res = 1000
        default_bg_color = "#FFFFFF"  # White
        # Target image resolution
        target_width = 10000
        target_height = 10000

        img_res = default_img_res
        clr = default_bg_color
        fig_size = (target_width / img_res, target_height / img_res)
        print("fig_size", fig_size)

        # Recommended: audit & repair DXF document before rendering
        auditor = self.doc.audit()
        # The auditor.errors attribute stores severe errors,
        # which *may* raise exceptions when rendering.
        if len(auditor.errors) != 0:
            raise Exception(
                "This DXF document is damaged and can't be converted! --> ",
            )
        else:
            image_buffer = io.BytesIO()
            # Get the actual bounds
            bboxobj = bbox.extents(self.msp)
            print("DXF bounds:", bboxobj)
            dxf_bounds_width = bboxobj.extmax.x - bboxobj.extmin.x
            dxf_bounds_height = bboxobj.extmax.y - bboxobj.extmin.y
            print("DXF bounds width height:", dxf_bounds_width, dxf_bounds_height)
            fig: Figure = plt.figure(figsize=fig_size, dpi=img_res)
            ax: Axes = fig.add_axes([0, 0, 1, 1])
            ax.set_xlim(bboxobj.extmin.x, bboxobj.extmax.x)
            ax.set_ylim(bboxobj.extmin.y, bboxobj.extmax.y)
            ctx = RenderContext(self.doc)
            ctx.set_current_layout(self.msp)
            ezdxf.addons.drawing.properties.MODEL_SPACE_BG_COLOR = clr
    
            out: MatplotlibBackend = MatplotlibBackend(ax)

            config:Configuration = Configuration(
                hatching_timeout=0.1, # was consuming a lot of memory out when dense hatch patterns.
            )
            Frontend(ctx, out, 
                config=config, 
                bbox_cache=bbox.Cache()).draw_layout(
                self.msp,
                finalize=True,
            )
            fig.savefig(image_buffer, format="png", dpi=img_res)
            print(" Converted Image Successfully")
            image_buffer.seek(0)

            # Print the final bounds used
            print(
                "Final drawing bounds:",
                {
                    "x_min": ax.get_xlim()[0],
                    "x_max": ax.get_xlim()[1],
                    "y_min": ax.get_ylim()[0],
                    "y_max": ax.get_ylim()[1],
                },
            )

            center_x_in_dxf_units = (ax.get_xlim()[1] + ax.get_xlim()[0]) / 2
            center_y_in_dxf_units = (ax.get_ylim()[1] + ax.get_ylim()[0]) / 2
            print("Center in DXF units:", center_x_in_dxf_units, center_y_in_dxf_units)

            center_x_in_unity_units = (
                center_x_in_dxf_units * self.dxf_data.unity3d_conversion_multiplier
            )
            center_y_in_unity_units = (
                center_y_in_dxf_units * self.dxf_data.unity3d_conversion_multiplier
            )

            print(
                "Center in Unity units:",
                center_x_in_unity_units,
                center_y_in_unity_units,
            )
            img = Image.open(image_buffer)
            width, height = img.size
            print("Image size in pixels:", width, height)
            image_size_in_dxf_units = (
                ax.get_xlim()[1] - ax.get_xlim()[0],
                ax.get_ylim()[1] - ax.get_ylim()[0],
            )

            print("Image size in DXF units:", image_size_in_dxf_units)
            dxf_units = self.doc.units
            print("DXF units:", dxf_units)
            magic_margin_x = bboxobj.extmin.x - ax.get_xlim()[0]
            magic_margin_y = bboxobj.extmin.y - ax.get_ylim()[0]
            magic_margin_x, magic_margin_y = 0, 0
            print(
                "Magic Margins:", magic_margin_x, magic_margin_y
            )  # not sure why this is needed - just based on trial and error - and also this isn't accurate.
            image_size_in_meters = (
                (image_size_in_dxf_units[0] + magic_margin_x)
                * self.dxf_data.unity3d_conversion_multiplier,
                (image_size_in_dxf_units[1] + magic_margin_y)
                * self.dxf_data.unity3d_conversion_multiplier,
            )
            print("Image size in meters:", image_size_in_meters)
            x_ratio_pixels_per_meter = width / image_size_in_meters[0]
            y_ratio_pixels_per_meter = height / image_size_in_meters[1]
            print(
                "X ratio pixels per meter:",
                x_ratio_pixels_per_meter,
                y_ratio_pixels_per_meter,
            )
            pixel_per_unity_setting_in_unity = 100
            scale_x_unity = pixel_per_unity_setting_in_unity / x_ratio_pixels_per_meter
            scale_y_unity = pixel_per_unity_setting_in_unity / y_ratio_pixels_per_meter
            print("Scale in Unity:", scale_x_unity, scale_y_unity)

            self.dxf_data.image_scale_x_unity = scale_x_unity
            self.dxf_data.image_scale_y_unity = scale_y_unity
            self.dxf_data.image_location_x_unity = center_x_in_unity_units
            self.dxf_data.image_location_y_unity = center_y_in_unity_units
            image_buffer.seek(0)
            return image_buffer


def create_binary_io_from_path(file_path: str) -> io.BytesIO:
    with open(file_path, "rb") as file:  # 'rb' opens the file in binary mode
        file_data = file.read()  # Read the entire file content into memory
    return io.BytesIO(file_data)  # Wrap the content into a BytesIO buffer


def main():
    parser: argparse.ArgumentParser = argparse.ArgumentParser()
    parser.add_argument("dxf_file", help="DXF file to process")
    args = parser.parse_args()
    binary_io = create_binary_io_from_path(args.dxf_file)
    dxf_processor = DXFFileProcessor(binary_io)


if __name__ == "__main__":
    main()
