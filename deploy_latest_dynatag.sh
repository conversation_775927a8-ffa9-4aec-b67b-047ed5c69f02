#!/bin/bash
set -ex  # -e: exit on error, -x: debug output

if [ -z "$1" ]; then
  echo "Error: No branch name provided."
  echo "Usage: ./deploy_latest_dynatag.sh <branch-name> <env-name>"
  exit 1
fi

if [ -z "$2" ]; then
  echo "Error: No env name provided."
  echo "Usage: ./deploy_latest_dynatag.sh <branch-name> <env-name>"
  exit 1
fi

BRANCH="$1"
ENV_NAME="$2"
echo "Deploying branch: $BRANCH to environment: $ENV_NAME"

whoami

# Ensure clean branch and sync with remote
git fetch origin
git checkout $BR<PERSON>CH
git pull origin $BRANCH

cd client/dynaconsole/
npm install
npm run build

cd ../..
python3.11 -m ops_tools.deploy -f ./client/dynaconsole/dist -e $ENV_NAME
