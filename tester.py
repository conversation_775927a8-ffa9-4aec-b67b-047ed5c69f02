import asyncio

from app_dxf.DXFUtils import DXFData
from common.storage.DiskFileCache import get_file



async def test2():
   #/Users/<USER>/code/mhxp/dynaserver/FILE_STORAGE/UNKNOWN_ACCOUNT/dxf-data/e874d593775f904f3f9631c68f02b80d-dxf-data.json
   #/Users/<USER>/code/mhxp/dynaserver/FILE_STORAGE/UNKNOWN_ACCOUNT/dxf-data/e874d593775f904f3f9631c68f02b80d-dxf-data.json
   conte = await get_file("e874d593775f904f3f9631c68f02b80d-dxf-data.json","UNKNOWN_ACCOUNT/dxf-data")
   print(conte)
   cached_dxf_data = DXFData.model_validate_json(
                    conte.read().decode("utf-8")
                )
   print(cached_dxf_data)
if __name__ == "__main__":
    asyncio.run(test2())
