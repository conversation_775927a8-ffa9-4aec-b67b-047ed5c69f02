from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from google.oauth2 import id_token
from google.auth.transport import requests
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from dotenv import load_dotenv
import os
from typing import Optional, Dict, Any

# Load environment variables
load_dotenv()

app = FastAPI(
    title="MHXP Auth API",
    description="Google OAuth2 Authentication API",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Google OAuth2 client configuration
GOOGLE_CLIENT_ID = os.getenv('VITE_GOOGLE_CLIENT_ID')
GOOGLE_CLIENT_SECRET = os.getenv('VITE_GOOGLE_CLIENT_SECRET')
REDIRECT_URI = os.getenv('VITE_REDIRECT_URI')

class TokenRequest(BaseModel):
    token: str

class CodeRequest(BaseModel):
    code: str

class TokenResponse(BaseModel):
    user: Dict[str, Any]
    tokens: Optional[Dict[str, str]] = None

@app.post("/api/auth/verify", response_model=TokenResponse)
async def verify_token(request: TokenRequest):
    """
    Verify a Google ID token and return user information
    """
    try:
        # Verify the ID token
        idinfo = id_token.verify_oauth2_token(
            request.token, 
            requests.Request(), 
            GOOGLE_CLIENT_ID
        )
        
        print("Token verification successful:", idinfo)
        return TokenResponse(user=idinfo)
    except Exception as e:
        print("Error verifying token:", str(e))
        raise HTTPException(status_code=500, detail="Token verification failed")

@app.post("/api/auth/callback", response_model=TokenResponse)
async def oauth_callback(request: CodeRequest):
    """
    Handle OAuth callback and exchange authorization code for tokens
    """
    try:
        print("Received code:", request.code)
        
        # Create OAuth2 flow
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": GOOGLE_CLIENT_ID,
                    "client_secret": GOOGLE_CLIENT_SECRET,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [REDIRECT_URI],
                    "scopes": ["openid", "email", "profile"]
                }
            },
            scopes=["openid", "email", "profile"]
        )
        
        # Exchange code for tokens
        flow.fetch_token(code=request.code)
        credentials = flow.credentials
        
        # Get ID token
        id_token_info = id_token.verify_oauth2_token(
            credentials.id_token,
            requests.Request(),
            GOOGLE_CLIENT_ID
        )
        
        return TokenResponse(
            user=id_token_info,
            tokens={
                'access_token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'id_token': credentials.id_token
            }
        )
    except Exception as e:
        print("Error in OAuth callback:", str(e))
        raise HTTPException(status_code=500, detail="Authentication failed")

@app.get("/")
async def root():
    """
    Root endpoint to check if the API is running
    """
    return {"status": "ok", "message": "MHXP Auth API is running"}

if __name__ == '__main__':
    import uvicorn
    print("Client ID:", GOOGLE_CLIENT_ID)
    uvicorn.run(app, host="0.0.0.0", port=3000) 