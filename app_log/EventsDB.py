from sqlalchemy import create_engine, Column, Integer, Float, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pydantic import BaseModel
from datetime import datetime
import time

Base = declarative_base()


# Pydantic model for validation and serialization


# SQLAlchemy ORM model for database storage
class UserEvents(Base):
    __tablename__ = "user_events"

    id = Column(Integer, primary_key=True, index=True)
    accountId = Column(String, nullable=False)
    userId = Column(String, nullable=False)
    clientVersion = Column(Integer, nullable=True)
    sessionId = Column(String, nullable=True)
    timestamp = Column(DateTime, nullable=False)
    count = Column(Float, nullable=False)
    event_type = Column(String, nullable=False)
    event_tag1 = Column(String, nullable=True)
    event_tag2 = Column(String, nullable=True)
    event_tag3 = Column(String, nullable=True)


class EventsDB:
    def __init__(self, db_url: str):
        self.db_url = db_url
        self.engine = None
        self.SessionLocal = None

    def start(self):
        """
        Initializes the database connection, creates tables, and sets up session local.
        """
        # Create the database engine
        self.engine = create_engine(
            self.db_url,
            connect_args={"check_same_thread": False},  # comment for postgres
        )

        # Create the sessionmaker
        self.SessionLocal = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )

        # Create all tables in the database
        Base.metadata.create_all(bind=self.engine)
        print("Database initialized and tables created.")

    def insert(self, event: UserEvents):
        """
        Inserts a new event into the database.
        """
        # Create a new SQLAlchemy session
        with self.SessionLocal() as db:
            try:
                db_event = event  # Convert if necessary
                db.add(db_event)
                db.commit()
                db.refresh(db_event)
                print(f"Event inserted: {db_event.id}")
            except Exception as e:
                db.rollback()
                print(f"Error inserting event: {e}")


# Example usage
if __name__ == "__main__":
    db_url = "sqlite:///./test.db"  # Replace with your PostgreSQL URL for production
    # db_url = "postgresql://postgres:postgres@localhost:5432/testp"
    events_db = EventsDB(db_url)

    # Initialize the database
    events_db.start()

    # Insert the event into the database
    for _ in range(100):
        new_event = UserEvents(
            accountId="Acc1",
            userId="User1",
            clientVersion=1,
            sessionId="session_123",
            timestamp=datetime.now(),
            count=1.0,
            event_type="test_event",
            event_tag1="tag1",
            event_tag2="tag2",
            event_tag3="tag3",
        )
        events_db.insert(new_event)
        print("Inserted ", db_url, new_event)
        time.sleep(2)
