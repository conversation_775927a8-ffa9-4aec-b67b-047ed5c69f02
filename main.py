from app_dxf.DXFHttp import DXFHttp
from app_homebot.HomeBotHttp import HomeBotHttp
from app_storage.ProjectHttp import ProjectHttp
from app_storage.StorageHttp import StorageHTTP
from common.httpserver import FastAPIApp
from app_log.LogHttp import LogHttp
from app_user.UserHttp import UserHttp

app = FastAPIApp()
LogHttp(app.app)
app.app.include_router(DXFHttp().router)
app.app.include_router(StorageHTTP().router)
app.app.include_router(ProjectHttp().router)
app.app.include_router(HomeBotHttp().router)
app.app.include_router(UserHttp().router)
app.start()
