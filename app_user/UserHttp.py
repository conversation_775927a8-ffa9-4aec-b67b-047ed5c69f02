import io
import os
from datetime import datetime, timedelta

from typing import List, Optional

from fastapi import APIRouter, File, Form, Header, Request, Response, UploadFile
from fastapi import HTTPException
from fastapi.params import Depends
from pydantic import BaseModel, Field
from requests import Session


from app_user.user_service import UserConstants, UserService
from common import httputils
from common.commonmodels import RequestContext

from fastapi.responses import FileResponse, StreamingResponse

from common.db.db import get_db
from common.db.entities import Build, Build_Status
from common.models.common_response_models import GenericKeyValueResponse
from common.storage.DiskFileCache import DiskFileCache
import jwt


class UserLoginResponse(BaseModel):
    access_token: str = Field(..., description="The access token for the user")
    token_expiry: float = Field(..., description="The expiry time of the access token")
    account_id: str = Field(..., description="The account id of the user")
    email: str = Field(..., description="The email of the user")


class UserHttp:

    SECRET_KEY = "your_secret_key"
    ALGORITHM = "HS256"

    def create_access_token_cookie(data: UserLoginResponse, response: Response):
        to_encode = data.model_dump()
        expire = (
            datetime.now()
            + timedelta(minutes=UserConstants.ACCESS_TOKEN_EXPIRE_MINUTES)
        ).timestamp()
        to_encode.update({"exp": expire})
        token = jwt.encode(to_encode, UserHttp.SECRET_KEY, algorithm=UserHttp.ALGORITHM)
        # Set HTTP-Only, Secure cookie
        response.set_cookie(
            key="access_token",
            value=token,
            httponly=True,
            secure=True,  # Works only on HTTPS if True
            samesite="None",
            max_age=UserConstants.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )
        return token

    def __init__(self):
        self.user_service = UserService()
        self.router = APIRouter(prefix="/user", tags=["users"])

        @self.router.post("/create", tags=["admin"])
        async def create_user(
            email: str = Form(...),
            password: str = Form(...),
            account_id: str = Form(...),
            db: Session = Depends(get_db),
        ):
            user = self.user_service.create_user(email, password, account_id, db)
            return user

        @self.router.post("/update", tags=["admin"])
        async def update_user(
            email: str = Form(...),
            password: str = Form(...),
            account_id: str = Form(...),
            db: Session = Depends(get_db),
        ):
            user = self.user_service.update_user(email, password, account_id, db)
            return user

        @self.router.post("/login")
        async def login_user(
            email: str = Form(...),
            password: str = Form(...),
            response: Response = None,
            db: Session = Depends(get_db),
        ):
            user = self.user_service.login_user(email, password, db)
            if user is None:
                raise HTTPException(status_code=401, detail="Invalid credentials")

            user_login_response = UserLoginResponse(
                access_token=user.access_token,
                token_expiry=user.token_expiry.timestamp(),
                account_id=user.account_id,
                email=user.email,
            )
            token = UserHttp.create_access_token_cookie(user_login_response, response)

            return user_login_response

        @self.router.post("/login/current")
        async def current_logged_in_user(
            db: Session = Depends(get_db),
            request: Request = None,
            response: Response = None,
        ):
            acess_token = request.cookies.get("access_token")
            if acess_token is None:
                raise HTTPException(status_code=401, detail="No logged in user")
            try:
                payload = jwt.decode(
                    acess_token, UserHttp.SECRET_KEY, algorithms=[UserHttp.ALGORITHM]
                )
            except jwt.ExpiredSignatureError:
                raise HTTPException(status_code=401, detail="Access token expired")
            except jwt.InvalidTokenError:
                raise HTTPException(status_code=401, detail="Invalid access token")
            login_response = UserLoginResponse(**payload)
            user = self.user_service.is_user_logged_in(
                login_response.email,
                login_response.access_token,
                db,
                refresh_token=True,
            )
            if user is None:
                raise HTTPException(
                    status_code=401, detail=f"User {login_response.email} not logged in"
                )

            relogin_user_response = UserLoginResponse(
                access_token=user.access_token,
                token_expiry=user.token_expiry.timestamp(),
                account_id=user.account_id,
                email=user.email,
            )
            token = UserHttp.create_access_token_cookie(relogin_user_response, response)

            return relogin_user_response

        @self.router.post("/logout")
        async def logout_user(
            db: Session = Depends(get_db),
            request: Request = None,
            response: Response = None,
        ):
            acess_token = request.cookies.get("access_token")
            if acess_token is not None:
                try:
                    payload = jwt.decode(
                        acess_token,
                        UserHttp.SECRET_KEY,
                        algorithms=[UserHttp.ALGORITHM],
                    )
                    email = payload["email"]
                    self.user_service.logout_user(email, db)
                    response.delete_cookie("access_token")
                except jwt.ExpiredSignatureError:
                    print("Access token expired")
                except jwt.InvalidTokenError:
                    print("Invalid access token")
            return {"status": "ok"}
