from app_user.UserHttp import <PERSON>r<PERSON>ttp, UserLoginResponse
from fastapi import Depends, HTTPException, Request
import jwt


def current_logged_in_user(
    request: Request = None,
) -> UserLoginResponse:
    access_token = request.cookies.get("access_token")
    if access_token is None:
        print("No access token found in cookies")
        raise HTTPException(status_code=401, detail="No logged-in user")
    try:
        payload = jwt.decode(
            access_token, UserHttp.SECRET_KEY, algorithms=[UserHttp.ALGORITHM]
        )
    except jwt.ExpiredSignatureError:
        print("Access token expired")
        raise HTTPException(status_code=401, detail="Access token expired")
    except jwt.InvalidTokenError:
        print("Invalid access token")
        raise HTTPException(status_code=401, detail="Invalid access token")

    login_response = UserLoginResponse(**payload)
    print(f"Logged in user: {login_response.email}")    
    return login_response
