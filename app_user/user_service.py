from datetime import datetime, timedelta
import uuid
from requests import Session
from common.db.entities import User


class UserConstants:
    ACCESS_TOKEN_EXPIRE_MINUTES = 45


class UserService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UserService, cls).__new__(cls)
        return cls._instance

    def create_user(
        self, email: str, password: str, account_id: str, db: Session
    ) -> User:
        user = User(
            email=email,
            password=password,
            account_id=account_id,
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def update_user(self, email: str, password: str, account_id: str, db: Session):
        user = db.query(User).filter(User.email == email).first()
        if user is None:
            return None
        user.password = password
        user.account_id = account_id
        db.update(user)
        db.commit()
        db.refresh(user)
        return user

    def is_user_logged_in(
        self, email: str, access_token: str, db: Session, refresh_token: bool = False
    ) -> User:
        user = (
            db.query(User)
            .filter(User.email == email, User.access_token == access_token)
            .first()
        )
        if user is None:
            return None
        if user.token_expiry < datetime.now():
            return None
        if refresh_token:
            user.access_token = str(uuid.uuid4())
            user.token_expiry = datetime.now() + timedelta(
                minutes=UserConstants.ACCESS_TOKEN_EXPIRE_MINUTES
            )
            db.commit()
            db.refresh(user)
        return user

    def login_user(self, email: str, password: str, db: Session) -> User:
        user = (
            db.query(User)
            .filter(
                User.email == email,
                User.password == password
            )
            .first()
        )
        if user is None:
            return None
        access_token = str(uuid.uuid4())
        token_expiry = datetime.now() + timedelta(
            minutes=UserConstants.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        user.access_token = access_token
        user.token_expiry = token_expiry
        db.commit()
        db.refresh(user)
        return user

    def logout_user(self, email: str, db: Session):
        user = db.query(User).filter(User.email == email).first()
        if user is None:
            print(f"User not found in logout: {email} , ignoring  ")
            return None
        user.access_token = None
        user.token_expiry = None
        db.commit()
        db.refresh(user)
        return user

    def update_user(self, user: User, db: Session):
        pass
