--
build flow √
    - apis:
        1. Get build_request from queue
        2. download file.
        2. Update build url

    - jekins to get_build request. If any. Else skip.

user onboarding
    - create user (through api)
    - client - sign up 
    - manage his status


user account 
    √- login user : check password, create token and send to client √
    √- client  - login flow √
    √- client - use token√
    √- client - token send √
    - apis - all apis accept token.
    - when token expires mid way.
    √ - token refresh √

merge and deploy changes
build jobs for DynaConsole
dynatag integration


security
- secure all apis
- check tenancy
- internal apis



backlog
 - build fail scenarios

- file api test
    - folders tenancy
    - what is the file / stream datastructure to use
    - storage
    
- √ c# calling test
- √ error cases test
- user construct
- project construct
- move dxf loading pieces here
- publish api
- agent work.

--

For Production
[] move from psycopg2-binary to psycopg2
----
1. Auth

2. Screens
    - Login
    - Account Allocation
    - Dashboard
        files
        builds
    



- optimizations

        async with aiofiles.open(full_path, "rb") as f:
            content = await f.read()

move this to stream return - to reduce load on memory
