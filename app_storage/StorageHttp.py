import io
import os
from typing import List, Optional
import zipfile
from fastapi import APIRouter, File, Form, Header, UploadFile
from fastapi import HTTPException
from common.commonmodels import RequestContext
import shutil
from fastapi.responses import FileResponse, StreamingResponse

from common.storage.DiskFileCache import DiskFileCache


class StorageHTTP:

    def __init__(self):
        self.router = APIRouter(prefix="/store")

        @self.router.post("/project")
        async def store_project(
            header_request_context: str = Header(...),
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
        ):
            request_ctx_obj = RequestContext.model_validate_json(header_request_context)
            if False and ( # disable for now.
                request_ctx_obj.accountId is None
                or request_ctx_obj.userId is None
                or request_ctx_obj.projectId is None
            ):

                raise HTTPException(
                    status_code=400,
                    detail="accountId, userId, and projectId are required",
                )
          
            # account_dir = os.path.join(self.STORAGE, request_ctx_obj.accountId)
            # project_dir = os.path.join(account_dir, request_ctx_obj.projectId)

            if files:
                for file in files:
                    await DiskFileCache.getInstance().put_file(
                        request_ctx_obj.accountId, file.file, "projects", file.filename
                    )
                    print("Wrote ", file.filename)

            return {"status": "ok"}

        @self.router.get("/project/{account_id}/{project_id}")
        def get_project_zip(account_id: str, project_id: str):

            project_dir = os.path.join(self.STORAGE, account_id, project_id)
            if not os.path.exists(project_dir):
                raise HTTPException(
                    status_code=404,
                    detail="Project not found: " + account_id + " : " + project_id,
                )

            # Create a BytesIO buffer to hold the zip data in memory
            zip_buffer = io.BytesIO()

            # Create a zip file in memory using the zipfile module
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
                for root, _, files in os.walk(project_dir):
                    for file in files:
                        # Skip the .zip file to avoid recursion
                        if file.endswith(".zip"):
                            continue
                        file_path = os.path.join(root, file)
                        # Add the file to the archive with its relative path
                        zip_file.write(
                            file_path, os.path.relpath(file_path, project_dir)
                        )

            # Seek back to the start of the buffer before sending the response
            zip_buffer.seek(0)

            # Return the zip file as a streaming response
            return StreamingResponse(
                zip_buffer,
                media_type="application/zip",
                headers={
                    "Content-Disposition": f"attachment; filename={project_id}.zip"
                },
            )
