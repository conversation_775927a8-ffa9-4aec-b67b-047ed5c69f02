import random
from typing import List
from sqlalchemy.orm import Session
from common.db.entities import Build, Build_Status
from common.db.db import get_db


Build_Queues_Ids = [1]


class BuildService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BuildService, cls).__new__(cls)
        return cls._instance

    def create_new_build_request(
        self,
        account_id,
        mhx_file_name,
        requested_by: str,
        bake_level: int,
        furniture_toggle: int,
        db: Session,
        build_status: Build_Status = Build_Status.QUEUED,
    ):
        build_number = -1
        build_queue_id = Build_Queues_Ids[random.randint(0, len(Build_Queues_Ids) - 1)]
        build = Build(
            account_id=account_id,
            project_name=mhx_file_name,
            build_number=-build_number,
            build_status=build_status.value,
            build_queue_id=build_queue_id,
            build_url=f"https://viz.spaceviz.ai/{account_id}/{mhx_file_name}/{build_number}/index.html",
            requested_by=requested_by,
            bake_level=bake_level,
            furniture_toggle=furniture_toggle,
        )
        db.add(build)
        db.commit()
        db.refresh(build)
        return build

    def read_build(self, build_id: int, db: Session):
        build = db.query(Build).filter(Build.id == build_id).first()
        return build

    def get_one_build_from_queue(self, queue_id: str, db: Session):

        build = (
            db.query(Build)
            .filter(
                Build.build_queue_id == queue_id,
                Build.build_status.in_(
                    [Build_Status.QUEUED.value, Build_Status.PROCESSING.value]
                ),
            )
            .order_by(Build.build_created_at.asc())
            .first()
        )
        return build

    def update_build_status(self, build_id: int, build_status: str, db: Session):
        db_build = db.query(Build).filter(Build.id == build_id).first()
        if db_build is None:
            raise ValueError("Build not found")

        db_build.build_status = build_status
        db.commit()
        db.refresh(db_build)
        return db_build

    def update_build_status_after_deploy(
        self, build_id: int, build_number: int, full_cdn_url: str, db: Session
    ):
        db_build = db.query(Build).filter(Build.id == build_id).first()
        if db_build is None:
            raise ValueError("Build not found")

        db_build.build_status = Build_Status.COMPLETED.value
        db_build.build_url = full_cdn_url
        db_build.build_number = build_number
        db.commit()
        db.refresh(db_build)
        return db_build

    def delete_build(self, build_id: int, db: Session):
        build = db.query(Build).filter(Build.id == build_id).first()
        if build is None:
            raise ValueError("Build not found")

        db.delete(build)
        db.commit()
        return {"detail": "Build deleted successfully"}

    def get_builds_by_project(self, account_id: str, project_name: str, db: Session):
        builds = (
            db.query(Build)
            .filter(Build.account_id == account_id, Build.project_name == project_name)
            .all()
        )
        return builds

    def get_builds_by_projects(
        self, account_id: str, project_names: List[str], db: Session
    ):
        builds = (
            db.query(Build)
            .filter(
                Build.account_id == account_id, Build.project_name.in_(project_names)
            )
            .all()
        )
        return builds
