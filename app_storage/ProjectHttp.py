import io
import os
import random
from typing import List, Optional
import tempfile
import zipfile

from pydantic import BaseModel
from app_user.UserHttp import UserLoginResponse
from app_user.user_deps import current_logged_in_user
from fastapi import APIRouter, File, Form, Header, UploadFile
from fastapi import HTT<PERSON>Exception
from fastapi.params import Depends
from requests import Session
from app_homebot.HomeBotHttp import StringResposne
from app_storage.builds_service import BuildService
from common import httputils
from common.commonmodels import RequestContext
import shutil
from fastapi.responses import FileResponse, StreamingResponse

from common.db.db import get_db
from common.db.entities import Build, Build_Status
from common.models.common_response_models import GenericKeyValueResponse
from common.storage.DiskFileCache import DiskFileCache
from ops_tools.deploy import Deployment


class ProjectHttp:

    def __init__(self):
        self.build_service = BuildService()
        self.router = APIRouter(prefix="/project", tags=["project"])

        @self.router.post("/store", tags=["admin"])
        async def store_project(
            header_request_context: str = Header(...),
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
        ):
            request_ctx_obj = RequestContext.model_validate_json(header_request_context)
            if False and (  # disable for now.
                request_ctx_obj.accountId is None
                or request_ctx_obj.userId is None
                or request_ctx_obj.projectId is None
            ):

                raise HTTPException(
                    status_code=400,
                    detail="accountId, userId, and projectId are required",
                )

            # account_dir = os.path.join(self.STORAGE, request_ctx_obj.accountId)
            # project_dir = os.path.join(account_dir, request_ctx_obj.projectId)

            if files:
                for file in files:
                    await DiskFileCache.getInstance().put_file(
                        request_ctx_obj.accountId, file.file, "projects", file.filename
                    )
                    print("Wrote ", file.filename)

            return {"status": "ok"}

        @self.router.get("/store/{account_id}")
        async def get_project_files_for_account(
            account_id: str, db: Session = Depends(get_db)
        ):
            files = await DiskFileCache.getInstance().get_files_for_account(
                accountId=account_id, folder="projects", file_type=".mhx"
            )
            print(f"Found {len(files)} files for account {account_id}")
            builds_for_all_projects = self.build_service.get_builds_by_projects(
                account_id, files, db
            )
            builds_by_project = {}
            for file in files:
                builds_by_project[file] = []
            for build in builds_for_all_projects:
                builds_by_project[build.project_name].append(build)
            return builds_by_project

        @self.router.get("/store/{account_id}/{mhx_file_name}")
        async def get_project_file(account_id: str, mhx_file_name: str):
            mhx_file = await DiskFileCache.getInstance().get_file(
                accountId=account_id,
                folder="projects",
                filename=mhx_file_name,
            )
            if not mhx_file:
                print("Project not found: " + account_id + " : " + mhx_file_name)
                raise HTTPException(
                    status_code=404,
                    detail="Project not found: " + account_id + " : " + mhx_file_name,
                )

            print("Found, Building project zip for: " + mhx_file_name)
            mhx_file.seek(0)
            response_json = StringResposne(
                message=f"File {mhx_file_name} for account {account_id} found"
            )
            files_to_return = []
            files_to_return.append(UploadFile(file=mhx_file, filename=mhx_file_name))
            return httputils.create_strean_response_json_file(
                response_json, files_to_return
            )

        @self.router.get("/store/download/{account_id}/{mhx_file_name}")
        async def get_project_file(account_id: str, mhx_file_name: str):
            mhx_file = await DiskFileCache.getInstance().get_file(
                accountId=account_id,
                folder="projects",
                filename=mhx_file_name,
            )
            if not mhx_file:
                print("Project not found: " + account_id + " : " + mhx_file_name)
                raise HTTPException(
                    status_code=404,
                    detail="Project not found: " + account_id + " : " + mhx_file_name,
                )

            print("Found, Building project zip for: " + mhx_file_name)
            mhx_file.seek(0)
            return StreamingResponse(
                mhx_file,
                media_type="application/octet-stream",  # Optional: can be set more specifically if needed
                headers={
                    "Content-Disposition": f'attachment; filename="{mhx_file_name}"'
                },
            )

        class BuildRequestParams(BaseModel):
            bake_level: Optional[int] = None
            furniture_toggle: Optional[int] = None

        @self.router.post("/build/{account_id}/{mhx_file_name}")
        async def create_build_request(
            account_id: str,
            mhx_file_name: str,
            body: BuildRequestParams,  # Changed from Form to use Pydantic model for JSON body
            header_request_context: str = Header(...),
            current_logged_in_user: UserLoginResponse = Depends(current_logged_in_user),
            db: Session = Depends(get_db),
        ):

            print(
                "current_logged_in_user : "
                + str(current_logged_in_user)
                + " bake_level : "
                + str(body.bake_level)
                + " furniture_toggle : "
                + str(body.furniture_toggle)
            )
            file: io.BytesIO = await DiskFileCache.getInstance().get_file(
                accountId=account_id, folder="projects", filename=mhx_file_name
            )
            if file is None:
                raise HTTPException(
                    status_code=404,
                    detail=f"Project file not found: {account_id} : {mhx_file_name}",
                )

            build_request = self.build_service.create_new_build_request(
                account_id=account_id,
                mhx_file_name=mhx_file_name,
                requested_by=current_logged_in_user.email,
                bake_level=body.bake_level,
                furniture_toggle=body.furniture_toggle,
                db=db,
            )
            return build_request

        @self.router.post("/build/status/{build_id}/processing")
        async def mark_processing(
            build_id: str,
            db: Session = Depends(get_db),
        ):
            self.build_service.update_build_status(
                build_id, Build_Status.PROCESSING.value, db
            )
            return {"status": "ok"}

        @self.router.get("/build/inqueue/{queue_id}")
        async def get_one_build_from_queue(
            queue_id: str,
            db: Session = Depends(get_db),
        ):

            build_request = self.build_service.get_one_build_from_queue(
                queue_id=queue_id,
                db=db,
            )
            if build_request is None:
                raise HTTPException(
                    status_code=204,
                    detail=f"Unprocessed Build Request not found in queue : {queue_id}",
                )
            file: io.BytesIO = await DiskFileCache.getInstance().get_file(
                accountId=build_request.account_id,
                folder="projects",
                filename=build_request.project_name,
            )

            if file is None:
                print(
                    f"Project file not found: {build_request.id} : {account_id} : {mhx_file_name}, marking as failed "
                )
                self.build_service.update_build_status(
                    build_request.id, Build_Status.FAILED.value(), db
                )
                raise HTTPException(
                    status_code=404,
                    detail=f"Project file not found: {account_id} : {mhx_file_name}",
                )
            file.seek(0)
            return httputils.create_strean_response_json_file(
                GenericKeyValueResponse(
                    props={
                        "build_id": build_request.id,
                        "filename": build_request.project_name,
                        "account_id": build_request.account_id,
                        "bake_level": build_request.bake_level,
                        "furniture_toggle": build_request.furniture_toggle,
                    }
                ),
                [UploadFile(file=file, filename=build_request.project_name)],
            )

        @self.router.post(
            "/build/status/cancel/{project_name}/{build_id}/", tags=["admin"]
        )
        async def mark_build_cancel(
            project_name: str,
            build_id: str,
            db: Session = Depends(get_db),
        ):
            """
            Mark the build as cancelled
            :build_id: The ID of the build to mark as cancelled
            """
            # for now ignore the project name
            self.build_service.update_build_status(
                build_id, Build_Status.CANCELLED.value, db
            )
            return {"status": "ok"}

        @self.router.post(
            "/create_completed_build/{account_id}/{mhx_file_name}/{build_by}",
            tags=["admin"],
        )
        async def create_completed_build(
            account_id: str,
            mhx_file_name: str,
            build_by: str,
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
            db: Session = Depends(get_db),
        ):

            mhx_file = await DiskFileCache.getInstance().get_file(
                accountId=account_id,
                folder="projects",
                filename=mhx_file_name,
            )
            if not mhx_file:
                print("Project not found: " + account_id + " : " + mhx_file_name)
                raise HTTPException(
                    status_code=404,
                    detail="Project not found: Account :["
                    + account_id
                    + "] ; MHX File :["
                    + mhx_file_name
                    + "]",
                )

            build_obj: Build = self.build_service.create_new_build_request(
                account_id=account_id,
                mhx_file_name=mhx_file_name,
                requested_by=build_by,
                bake_level=-1,
                furniture_toggle=-1,
                db=db,
                build_status=Build_Status.MANUAL,
            )
            temp_dir_path = None
            # Create a temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = temp_dir
                # Unzip the files into the temporary directory
                for file in files:
                    zip_file_path = file.file
                    with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
                        zip_ref.extractall(temp_dir)
                    print(f"Unzipped {file.filename} to {temp_dir}")

                if not os.listdir(temp_dir_path):
                    raise HTTPException(
                        status_code=400, detail="Illegal argument: Zip is invalid."
                    )
                print(f"Preparing to deploy contents of {temp_dir_path}...")
                files_in_temp_dir = os.listdir(temp_dir_path)
                print(f"Files in temporary directory: {files_in_temp_dir}")
                deploy = Deployment(
                    "prod_viz",
                    account_name=account_id,
                    project_name=mhx_file_name,
                    build_number=build_obj.id,
                    triggered_by=(
                        build_obj.requested_by
                        if (build_obj and build_obj.requested_by)
                        else "unknown"
                    ),
                    bake_levle=build_obj.bake_level,
                    furniture_toggle=build_obj.furniture_toggle,
                )

                print(
                    f"Deploying project build for {account_id} : {mhx_file_name} -> {build_obj} - {input_json} - {len(files)} files"
                )

                await deploy.deploy(temp_dir_path)
                print(
                    "Deploy Finshed "
                    + deploy.full_cdn_url
                    + " :: "
                    + deploy.url_context
                )
                self.build_service.update_build_status_after_deploy(
                    build_obj.id, build_obj.id, deploy.full_cdn_url, db
                )
            return {
                "status": "ok",
                "fullurl": deploy.full_cdn_url,
                "urlcontext": deploy.url_context,
            }

        @self.router.post(
            "/build_deploy/{account_id}/{mhx_file_name}/{build_id}/{build_number}"
        )
        async def deploy_project_build(
            account_id: str,
            mhx_file_name: str,
            build_id: str,
            build_number: str,
            header_request_context: str = Header(...),
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
            db: Session = Depends(get_db),
        ):

            build_obj: Build = self.build_service.read_build(build_id, db)
            temp_dir_path = None
            # Create a temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = temp_dir
                # Unzip the files into the temporary directory
                for file in files:
                    zip_file_path = file.file
                    with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
                        zip_ref.extractall(temp_dir)
                    print(f"Unzipped {file.filename} to {temp_dir}")

                if not os.listdir(temp_dir_path):
                    raise HTTPException(
                        status_code=400, detail="Illegal argument: Zip is invalid."
                    )
                print(f"Preparing to deploy contents of {temp_dir_path}...")
                files_in_temp_dir = os.listdir(temp_dir_path)
                print(f"Files in temporary directory: {files_in_temp_dir}")
                deploy = Deployment(
                    "prod_viz",
                    account_name=account_id,
                    project_name=mhx_file_name,
                    build_number=build_id,
                    triggered_by=(
                        build_obj.requested_by
                        if (build_obj and build_obj.requested_by)
                        else "unknown"
                    ),
                    bake_levle=build_obj.bake_level,
                    furniture_toggle=build_obj.furniture_toggle,
                )

                print(
                    f"Deploying2 project build for {account_id} : {mhx_file_name} -> {build_number} - {input_json} - {len(files)} files"
                )

                await deploy.deploy(temp_dir_path)
                print(
                    "Deploy Finshed2 "
                    + deploy.full_cdn_url
                    + " :: "
                    + deploy.url_context
                )
                self.build_service.update_build_status_after_deploy(
                    build_id, build_number, deploy.full_cdn_url, db
                )
            return {
                "status": "ok",
                "fullurl": deploy.full_cdn_url,
                "urlcontext": deploy.url_context,
            }
