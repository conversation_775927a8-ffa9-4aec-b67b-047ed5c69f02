# DynaConsole

A responsive web application for managing projects and their builds. Built with React, TypeScript, and Tailwind CSS.

## Features

- User authentication
- Responsive dashboard with sidebar navigation
- Project management with expandable build lists
- Build lifecycle tracking (in queue, processing, complete)
- User settings management

## Project Structure

- `/src/features` - Core features organized by domain
  - `/auth` - Authentication-related components and context
  - `/projects` - Project and build management
  - `/settings` - User settings
- `/src/components` - Reusable UI components
  - `/layout` - Layout components (sidebar, dashboard layout)
  - `/ui` - Small reusable UI elements
- `/src/lib` - Utilities and helper functions

## Getting Started

### Prerequisites

- Node.js (v18+)
- npm (v9+)

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Login

Use any email and password combination to log in to the demo application.

## Usage

1. Log in with any email and password
2. Navigate through the dashboard using the sidebar
3. View and expand projects to see their builds
4. Trigger new builds for any project
5. Check user settings

## Build

```bash
npm run build
```

## Development Notes

- This project uses Tailwind CSS for styling
- React Router for navigation
- TypeScript for type safety
- Mock data is used for demonstration purposes
