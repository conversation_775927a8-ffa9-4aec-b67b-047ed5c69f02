<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/mhxp.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MHXP DynaConsole </title>
  </head>
  <body>
        <!-- BEGIN WEBGL FILE BROWSER LIB -->
        <style>
          #fb_popup_background.highlight {
              border: 3px dashed #ccc;
              border-radius: 26px;
              border-color: aquamarine;
          }
          .disableSelection {
              -webkit-user-select: none;
              -khtml-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              -o-user-select: none;
              user-select: none;
          }
      </style>
  
      <div id="fileBrowserPopup" style="display: none; margin: 0%; position: absolute; width: 100%; height: 100%; left: 0; top: 0; bottom: 0; right: 0;">
          <img class="disableSelection" style="position: absolute; width: 100%; height: 100%; background-color: #000000; filter: opacity(0.7) blur(4px); " />
  
          <div id="fb_popup">
              <img id="fb_popup_background" src="WebDynaTag/FileBrowser/popup_background.png" style="position: fixed; top: 0;  right: 0; bottom: 0; left: 0; margin: auto; width: 502.5px; height: 206px;" />
              <div>
                  <img src="WebDynaTag/FileBrowser/popup_header.png" style="position: fixed; top: -146px;  right: 0; bottom: 0; left: 0; margin: auto; width: 502.5px; height: 60.5px;" />
                  <strong id="fb_popup_header_title" style="position: fixed; top: -110px;  right: -20px; bottom: 0; left: 0; margin: auto; width: 500px; height: 58px; color: white;">
                      File Browser
                  </strong>
              </div>
              <div id="open_file_form">
                  <strong id="fb_popup_description_title" style="position: fixed; top: 0px;  right: 0px; bottom: 0; left: 0; margin: auto; width: 500px; height: 58px; text-align: center; color: black;">
                      Select file to load or use drag & drop
                  </strong>
  
                  <label for="fileToUpload">
                      <img src="WebDynaTag/FileBrowser/select_button.png" style="position: fixed; top: 0px;  right: 250px; bottom: -80px; left: 0; margin: auto; width: 193.5px; height: 41px;" />
                      <strong id="fb_popup_select_button_title" style="position: fixed; top: 0px;  right: 250px; bottom: -100px; left: 0; margin: auto; width: 193.5px; height: 41px; text-align: center; color: white;">
                          Select
                      </strong>
                  </label>
                  <input type="file" name="fileToUpload" id="fileToUpload" style="width: 0px; height: 0px;" onchange="fbLoadFiles(event.target.files);return false;" />
  
                  <label for="closePopup">
                      <img src="WebDynaTag/FileBrowser/close_button.png" style="position: fixed; top: 0px;  right: -250px; bottom: -80px; left: 0; margin: auto; width: 193.5px; height: 41px;" />
                      <strong id="fb_popup_close_button_title" style="position: fixed; top: 0px;  right: -245px; bottom: -100px; left: 0; margin: auto; width: 193.5px; height: 41px; text-align: center; color: white;">
                          Close
                      </strong>
                  </label>
                  <input type="button" name="closePopup" id="closePopup" style="width: 0px; height: 0px;" onclick="requestCloseFileBrowserForOpen()" />
              </div>
          </div>
      </div>
  
      <script type='text/javascript'>
          // ************************ Drag and drop ***************** //
          let dropArea = document.getElementById("fb_popup");
          let dropAreaBG = document.getElementById("fb_popup_background");
  
          // Prevent default drag behaviors
          ;['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
              dropArea.addEventListener(eventName, preventDefaults, false);
              document.body.addEventListener(eventName, preventDefaults, false);
          })
  
          // Highlight drop area when item is dragged over it
          ;['dragenter', 'dragover'].forEach(eventName => {
              dropArea.addEventListener(eventName, highlight, false);
          })
  
          ;['dragleave', 'drop'].forEach(eventName => {
              dropArea.addEventListener(eventName, unhighlight, false);
          })
  
          // Handle dropped files
          dropArea.addEventListener('drop', handleDrop, false);
  
          function preventDefaults (e) {
              e.preventDefault();
              e.stopPropagation();
          }
  
          function highlight(e) {
              dropAreaBG.classList.add('highlight');
          }
  
          function unhighlight(e) {
              dropAreaBG.classList.remove('highlight');
          }
  
          function handleDrop(e) {
              var dt = e.dataTransfer;
              var items = dt.items;
  
              getFilesDataTransferItems(items).then(files => {
                  fbLoadFiles(files);
              });
          }
  
          function getFilesDataTransferItems(dataTransferItems) {
              function traverseFileTreePromise(item, path = "", files) {
                  return new Promise(resolve => {
                      if (item.isFile) {
                          item.file(file => {
                              files.push(file);
                              resolve(file);
                          });
                      } else if (item.isDirectory) {
                          let dirReader = item.createReader();
                          dirReader.readEntries(entries => {
                              let entriesPromises = [];
                              for (let entr of entries)
                                  entriesPromises.push(
                                      traverseFileTreePromise(entr, (path || "") + item.name + "/", files)
                                  );
                              resolve(Promise.all(entriesPromises));
                          });
                      }
                  });
              }
  
              let files = [];
              return new Promise((resolve, reject) => {
                  let entriesPromises = [];
                  for (let it of dataTransferItems)
                      entriesPromises.push(
                          traverseFileTreePromise(it.webkitGetAsEntry(), null, files)
                      );
                  Promise.all(entriesPromises).then(entries => {
                      resolve(files);
                  });
              });
          }
      </script>
  
      <script type='text/javascript'>
          function callFBFunctionByName(functionName, parameter){
              switch(functionName){
                  case "initializeFBLibrary":
                      initializeFBLibrary();
                      break;
                  case "openFileBrowserForLoad":
                      openFileBrowserForLoad(parameter);
                      break;
                  case "cleanupFB":
                      cleanupFB();
                      break;       
                  case "closeFileBrowserForOpen":
                      closeFileBrowserForOpen();
                      break;
                  case "saveFile":
                      saveFileFB(parameter);
                      break;
                  case "setLocalization":
                      setLocalizationFB(parameter);
                      break;
              }
          }
          document.callFBFunctionByName = callFBFunctionByName;
  
          function initializeFBLibrary(){
              document.fbPopup = document.getElementById("fileBrowserPopup");
              document.fbOpenFilePopupInput = document.getElementById("fileToUpload");
  
              document.fbStorage = { 
                  initialized: true,
                  loadedFiles: {},
                  dataPointers: []
              };
          }
  
          function openFileBrowserForLoad(parameters){
              if(document.fbStorage.initialized !== true)
                  return;
  
              var typesFilter = parameters[0];
              var isMultipleSelection = parameters[1] === 1 ? true : false;
              var isFolder = parameters[2] === 1 ? true : false;
  
              if(document.fbOpenFilePopupInput.hasAttribute('multiple'))
                  document.fbOpenFilePopupInput.removeAttribute('multiple');
  
              if (document.fbOpenFilePopupInput.hasAttribute('webkitdirectory'))
                  document.fbOpenFilePopupInput.removeAttribute('webkitdirectory');
  
              if(isMultipleSelection && !isFolder)
                  document.fbOpenFilePopupInput.setAttribute('multiple', '');
  
              if(isFolder)
                  document.fbOpenFilePopupInput.setAttribute('webkitdirectory', '');
  
              document.fbOpenFilePopupInput.accept = typesFilter;
          document.fbPopup.style.display = "flex";
          }
  
          function closeFileBrowserForOpen(){
              if(document.fbStorage.initialized !== true)
                  return;
              document.fbPopup.style.display = "none";
          }
  
          function cleanupFB() {
              if(document.fbStorage.initialized !== true)
                  return;
              document.fbStorage.loadedFiles = {};
              document.fbStorage.dataPointers = [];
          }
  
          function saveFileFB(fileData){
              if(document.fbStorage.initialized !== true)
                  return;
  
              let fileInfo = {
                  status: true,
                  message: "",
                  name: fileData.name
              };
  
              try{
                  var contentType = 'application/octet-stream';
                  var a = document.createElement('a');
                  var blob = new Blob([fbBase64ToBytesArray(fileData.data)], {
                      'type': contentType
                  });
                  a.href = window.URL.createObjectURL(blob);
                  a.download = fileData.name;
  
                  if (document.createEvent) {
                      var event = document.createEvent('MouseEvents');
                      event.initEvent('click', true, true);
                      a.dispatchEvent(event);
                  } else {
                      a.click();
                  }
  
                  gameInstance.sendMessage(libraryHandlerObjectName, "HandleFileSaved", JSON.stringify(fileInfo));
              }
              catch(ex){
                  fileInfo.status = false;
                  fileInfo.message = ex.message;
  
                  gameInstance.sendMessage(libraryHandlerObjectName, "HandleFileSaved", JSON.stringify(fileInfo));
              }
          }
  
          const libraryHandlerObjectName = "[FGFileBrowser]";
  
          function fbLoadFiles(files) {
            console.log("fbLoadFiles", gameInstance);
              gameInstance.sendMessage(libraryHandlerObjectName, 'SetAmountOfFilesToBeLoaded', Array.from(files).filter(item => !item.name.endsWith(".DS_Store")).length);
  
              for (var i = 0, f; f = files[i]; i++) {
                  // ignore Mac hidden file
                  if (f.name.endsWith(".DS_Store"))
                      continue;
  
                  var reader = new FileReader();
                  reader.onload = (function (file) {
                      return function (fileloadEvent) {
                          let loadedFileInfo = fileloadEvent.target.result;
                  
                          document.fbStorage.loadedFiles[file.name] = {
                              info: loadedFileInfo
                          };
  
                          let extensionSplit = file.name.split('.');
                          let extension = extensionSplit[extensionSplit.length - 1];
                          let name = file.name.replace(extension, "");
                          name = name.substring(0, name.length - 1);
  
                          let loadedFile = {
                              fullName: file.name,
                              name: name,
                              path: "unavailable-in-web",
                              length: loadedFileInfo.byteLength,
                              size: file.size,
                              extension: extension
                          };
  
                          gameInstance.sendMessage(libraryHandlerObjectName, 'HandleLoadedFile', JSON.stringify(loadedFile));
                      }
                  })(f);
                  
                  reader.readAsArrayBuffer(f);
              }
        document.fbOpenFilePopupInput.value = "";
          }
  
          function requestCloseFileBrowserForOpen() {
              gameInstance.sendMessage(libraryHandlerObjectName, "CloseFileBrowserForOpen");
          }
  
          function fbBase64ToBytesArray(base64) {
              var binary_string = window.atob(base64);
              var len = binary_string.length;
              var bytes = new Uint8Array(len);
              for (var i = 0; i < len; i++) {
                  bytes[i] = binary_string.charCodeAt(i);
              }
              return bytes.buffer;
          }
  
          function setLocalizationFB(parameter){
              switch(parameter.key){
                  case "HEADER_TITLE":
                      document.getElementById("fb_popup_header_title").innerHTML = parameter.value;
                      break;
                  case "DESCRIPTION_TEXT":
                      document.getElementById("fb_popup_description_title").innerHTML = parameter.value;
                      break;
                  case "SELECT_BUTTON_CONTENT":
                      document.getElementById("fb_popup_select_button_title").innerHTML = parameter.value;
                      break;
                  case "CLOSE_BUTTON_CONTENT":
                      document.getElementById("fb_popup_close_button_title").innerHTML = parameter.value;
                      break;
              }
          }
  
      </script>
      <!-- END WEBGL FILE BROWSER LIB -->
    <div id="root"></div>
    <div id="modal-root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
