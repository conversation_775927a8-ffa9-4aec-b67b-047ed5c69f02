import { Project, Build, UserLoginResponse } from "./types";
import axios from "axios";



// Create an Axios instance with the base URL and withCredentials set to true
const api = axios.create({
  baseURL: import.meta.env.VITE_API_DYNASERVER, // adjust if needed
  withCredentials: true, // ensures cookies are sent/received
});

// Fetch all projects with their builds
export const fetchProjects = async (userContext: {
  user: UserLoginResponse | null;
  isAuthenticated: boolean;
}): Promise<Project[]> => {
  console.log(
    "USER CONTEXT",
    userContext.user,
    userContext.user?.account_id,
    userContext.isAuthenticated
  );

  const endpoint = `/project/store/${userContext.user?.account_id}`;
  console.log("Fetch endpoint", api.defaults.baseURL + endpoint);

  const response = await api.get(endpoint);
  const data = response.data;
  console.log("Data", data);

  // Convert the data into Project array
  const projs: Project[] = Object.entries(data).map(([projectName, builds]) => {
    return {
      id: projectName, // Using project name as ID
      name: projectName,
      description: projectName,
      builds: (builds as any[]).map((build) => ({
        id: build.id.toString(),
        status: build.build_status,
        url: build.build_url,
        createdAt: build.build_created_at,
        projectId: projectName,
        requested_by: build.requested_by,
      })),
    };
  });

  return projs;
};

export const get_current_user = async (): Promise<UserLoginResponse | null> => {
  try {
    const response = await api.post("/user/login/current");
    console.log("Current user", response.data);
    console.log("Status code", response.status);
    console.log("get_current_user response", response.data);
    if (response.status != 200) {
      return null;
    }
    return response.data;
  } catch (error) {
    console.info("get_current_user failed:", error);
    return null;
  }
};

export const logout_user = async (): Promise<void> => {
  try {
    const response = await api.post("/user/logout");
    console.log("Logout response", response.data);
  } catch (error) {
    console.error("logout_user failed:", error);
  }
};
export const login_api = async (
  email: string,
  password: string
): Promise<UserLoginResponse> => {
  try {
    const response = await api.post(
      "/user/login",
      new URLSearchParams({ email, password }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Accept: "application/json",
        },
      }
    );

    console.log("Status code", response.status);
    console.log("Login response", response.data);
    return response.data;
  } catch (error) {
    console.error("Login failed:", error);
    throw new Error("Login failed");
  }
};

export const cancel_build = async (
  build: Build,
): Promise<void> => {
  try {
    if (!build) {
      console.log("No build to cancel");
      return;
    }
    const response = await api.post(
      `/project/build/status/cancel/${encodeURIComponent(build.projectId)}/${build?.id}/`,
      {
        headers: {
          "Content-Type": "application/json",
          "header-request-context": "{}",
        },
      }
    );

    console.log("Status code", response.status);
    console.log("Cancel build response", response.data);
    build.status = 'cancelled';
    return response.data;
  } catch (error) {
    console.error("Cancel build failed:", error);
  }

}
// Create a new build for a project
export const createBuild = async (
  user: UserLoginResponse | null,
  projectId: string,
  bakeLevel: number, furnitureToggle: number
): Promise<Build> => {
  if (!user) {
    throw new Error("User not found");
  }
  console.log("User", user);
  console.log("Project ID", projectId);

  try {
    const response = await api.post(
      `/project/build/${user.account_id}/${projectId}`,
      {
        bake_level: bakeLevel,
        furniture_toggle: furnitureToggle,
      }, // empty body
      {
        headers: {
          "Content-Type": "application/json",
          "header-request-context": "{}",
        },
      }

    );

    const build_details = response.data;
    console.log("Build details", build_details);

    // Create a new build object
    const newBuild: Build = {
      id: build_details.build_number,
      status: "queued",
      createdAt: build_details.build_created_at,
      url: build_details.build_url,
      projectId: projectId,
    };

    return newBuild;
  } catch (error) {
    console.error("Failed to create build:", error);
    throw error;
  }
};
