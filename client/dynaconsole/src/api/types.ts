// Project build types
export type BuildStatus = "manual" | "queued" | "processing" | "completed" | "failed" | "cancelled";

export interface Build {
  id: string;
  status: BuildStatus;
  url?: string;
  createdAt: string;
  projectId: string;
  requested_by?: string;
}

// Project type
export interface Project {
  name: string;
  description: string;
  builds: Build[];
}

export interface UserLoginResponse {
  email: string;
  access_token: string;
  token_expiry: number;
  account_id: string;
}
