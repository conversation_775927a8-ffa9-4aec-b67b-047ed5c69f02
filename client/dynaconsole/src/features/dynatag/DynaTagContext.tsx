import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from "react";
import { useUnityContext } from "react-unity-webgl";
import { useAuth } from "../auth/AuthContext";

// Define Unity files prefix
const WEBGL_ROOT = "WebDynaTag/Build";
const WEBGL_FILES_PREFIX = `${WEBGL_ROOT}/WebDynaTag`;
const BR_EXTENSION = "";//".br";

// Define the correct Unity context type
type UnityContextType = ReturnType<typeof useUnityContext>;

type DynaTagContextType = UnityContextType & {
  currentSelectedProject: string;
  setCurrentSelectedProject: (project: string) => void;
  dyntagLoaded: boolean;
  setDyntagLoaded: (loaded: boolean) => void;
  //setMessageFromUnity: (message: string) => void;
  //sendAuthInfoToUnity: (user: any) => void;
};

// Create a global Unity Context (initialize as 'null' but use 'as any' to bypass TS)
const DynaTagContext = createContext<DynaTagContextType>(null as any);

export const DynaTagProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { user } = useAuth();
  const [currentSelectedProject, setCurrentSelectedProject] =
    useState<string>("");
  const [dyntagLoaded, setDyntagLoaded] = useState<boolean>(false);
  // Call useUnityContext at the top level - not inside useState callback
  // read build.json nad get the productVersion from that json
  var build_number = Date.now(); //temp
  const unityContext = useUnityContext({
    loaderUrl: `${WEBGL_FILES_PREFIX}.loader.js`,
    dataUrl: `${WEBGL_FILES_PREFIX}.data${BR_EXTENSION}`,
    frameworkUrl: `${WEBGL_FILES_PREFIX}.framework.js${BR_EXTENSION}`,
    codeUrl: `${WEBGL_FILES_PREFIX}.wasm${BR_EXTENSION}`,
    productName: "DynaTag",
    productVersion: build_number.toString(),
    companyName: "MHXP",
  });

  (window as any).gameInstance = unityContext;
  //  console.log("gameInstance", (window as any).gameInstance);
  const UR_OnDyntagAppLoad = (event: Event) => {
    console.log("UR_OnDyntagAppLoad : Message from Unity:", event, user);
    setDyntagLoaded(true);

  };
  useEffect(() => {
    console.log("dyntagLoaded", dyntagLoaded);
    if (dyntagLoaded) {
      console.log("Sending auth info to Unity:", user);
      unityContext.sendMessage("ReactBridge", "RU_BaseUrlReceived", import.meta.env.VITE_API_DYNASERVER);
      unityContext.sendMessage(
        "ReactBridge",
        "RU_AuthInfoReceived",
        JSON.stringify(user)
      );
    }
  }, [dyntagLoaded]);

  useEffect(() => {
    console.log("Adding event listener");
    // window.addEventListener("SendMessageToReact", handleUnityMessage);
    window.addEventListener("UR_OnDyntagAppLoad", UR_OnDyntagAppLoad);

    return () => {
      //   window.removeEventListener("SendMessageToReact", handleUnityMessage);
      window.removeEventListener("UR_OnDyntagAppLoad", UR_OnDyntagAppLoad);
    };
  }, [addEventListener, removeEventListener]);


  useEffect(() => {
    console.log(
      "dyntagLoaded",
      dyntagLoaded,
      "currentSelectedProject",
      currentSelectedProject
    );
    if (currentSelectedProject && dyntagLoaded) {
      console.log("Sending auth info to Unity:", user);
      unityContext.sendMessage(
        "ReactBridge",
        "RU_AuthInfoReceived",
        JSON.stringify(user)
      );
      console.log("Sending project to Unity:", currentSelectedProject);
      unityContext.sendMessage(
        "ReactBridge",
        "RU_OpenProject",
        currentSelectedProject
      );
    }
  }, [currentSelectedProject, dyntagLoaded]);

  return (
    <DynaTagContext.Provider
      value={{
        ...unityContext,
        currentSelectedProject,
        setCurrentSelectedProject,
        dyntagLoaded,
        setDyntagLoaded,
      }}
    >
      {children}
    </DynaTagContext.Provider>
  );
};

export const useGlobalDynaTag = (): DynaTagContextType => {
  const context = useContext(DynaTagContext);
  if (!context) {
    throw new Error("useGlobalUnity must be used within a UnityProvider");
  }
  return context;
};
