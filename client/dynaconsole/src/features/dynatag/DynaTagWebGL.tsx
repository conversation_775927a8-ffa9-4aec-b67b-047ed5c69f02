import { Unity } from "react-unity-webgl";
import { useGlobalDynaTag } from "./DynaTagContext";
import { useState, useEffect } from "react";

const DynaTagWebGL: React.FC = () => {
  const {
    unityProvider,

    loadingProgression,
    currentSelectedProject,
    dyntagLoaded,
    setDyntagLoaded,
    addEventListener,
    removeEventListener,
  } = useGlobalDynaTag();
  //const { user } = useAuth();
  const [messageFromUnity, setMessageFromUnity] = useState("");


  const handleUnityMessage = (event: Event) => {
    console.log("Message from Unity:", event);
    const customEvent = event as CustomEvent<string>;

    setMessageFromUnity(customEvent?.detail ?? "no param");
  };

  useEffect(() => {
    console.log("Adding event listener");
    window.addEventListener("SendMessageToReact", handleUnityMessage);
    //  window.addEventListener("UR_OnDyntagAppLoad", UR_OnDyntagAppLoad);

    return () => {
      window.removeEventListener("SendMessageToReact", handleUnityMessage);
      // window.removeEventListener("UR_OnDyntagAppLoad", UR_OnDyntagAppLoad);
    };
  }, [addEventListener, removeEventListener, handleUnityMessage]);

  useEffect(() => {
    console.log("messageFromUnity", messageFromUnity);
  }, [messageFromUnity]);

  useEffect(() => {
    console.log("currentSelectedProject changed, reloading", currentSelectedProject);
    setDyntagLoaded(false);
  }, [currentSelectedProject]);


  useEffect(() => {
    console.log("dyntagLoaded on webgl", dyntagLoaded);
  }, [dyntagLoaded]);



  return (
    <div className="container-unity" style={{ width: "100%", height: "100%" }}>
      {dyntagLoaded === false && (
        // We'll conditionally render the loading overlay if the Unity
        // Application is not loaded.
        <div className="loading-overlay-unity">
          {loadingProgression < 1 ? (
            <p>Loading... ({Math.round(loadingProgression * 100)}%)</p>
          ) : (
            <p>Initializing Dynatag...</p>
          )}

        </div>
      )}
      <Unity className="unity" key={currentSelectedProject}
        unityProvider={unityProvider} style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
};


export default DynaTagWebGL;
