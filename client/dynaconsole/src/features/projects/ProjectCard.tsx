import { useState } from "react";
import { Project, Build } from "../../api/types";
import BuildStatusBadge from "../../components/ui/BuildStatusBadge";
import ConfirmationModal from "../../components/ui/ConfirmationModal";
import { useGlobalDynaTag } from "../dynatag/DynaTagContext";
import { useAuth } from "../auth/AuthContext";
import { cancel_build } from "../../api/api";

interface ProjectCardProps {
  project: Project;
  onTriggerBuild: (projectId: string, bakeLevel: number, furnitureToggle: number) => void;
  onOpen: (projectId: string) => void;
}

export default function ProjectCard({
  project,
  onTriggerBuild,
  onOpen,
}: ProjectCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showConfirmKillModal, setShowConfirmKillModal] = useState(false);
  const [selectedBuild, setSelectedBuild] = useState<Build | null>(null);
  const { currentSelectedProject } = useGlobalDynaTag();

  const [bakeLevel, setBakeLevel] = useState(0); // Default for "Off"
  const [furnitureToggle, setFurnitureToggle] = useState(0); // Default for "Off"

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const { user } = useAuth();

  const handleTriggerBuild = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowConfirmModal(true);
  };

  const handleBuildKillClick = (e: React.MouseEvent, build: Build) => {
    e.stopPropagation();
    setShowConfirmKillModal(true);
    setSelectedBuild(build);
  };

  const handleOpen = (e: React.MouseEvent) => {
    e.stopPropagation();
    onOpen(project.name);
  };

  const confirmBuild = () => {
    onTriggerBuild(project.name, bakeLevel, furnitureToggle); // Pass state values
    setShowConfirmModal(false);
    setIsExpanded(true); // Expand the row to show the new build
  };

  const confirmCancelBuild = async () => {
    console.log("Cancelling build", project.name, selectedBuild?.id);
    setIsExpanded(true); // Expand the row to show the new build
    setShowConfirmKillModal(false);
    setSelectedBuild(null);
    if (!selectedBuild) {
      console.error("No build selected for cancellation");
      return;
    }
    await cancel_build(selectedBuild);
    setIsExpanded(false);
  };


  const cancelBuild = () => {
    setShowConfirmModal(false);
    setShowConfirmKillModal(false);
    setSelectedBuild(null);
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString();
  };

  return (
    <div className="card">
      {/* Card header */}
      <div
        className="px-4 py-5 sm:px-6 flex justify-between items-center cursor-pointer hover:bg-gray-50"
        onClick={toggleExpand}
      >
        <div className="flex-1 min-w-0 pr-4">
          <h3 className="text-lg font-medium text-gray-900 truncate">
            {project.name}
            {project.builds.some((build) => build.status === "processing") && (
              <span className="ml-2 inline-block">
                <span className="relative flex size-4">
                  <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-sky-400 opacity-75"></span>
                  <span className="relative inline-flex size-4 rounded-full bg-sky-500"></span>
                </span>
              </span>
            )}
          </h3>
          <p className="text-sm text-gray-500 truncate">
            {project.description}
          </p>
        </div>
        <div className="flex items-center flex-shrink-0">
          <a className="p-2" href={import.meta.env.VITE_API_DYNASERVER + "/project/store/download/" + user?.account_id + "/" + project.name}
            download>
            Download
          </a>
          <button
            type="button"
            onClick={handleOpen}
            className="btn btn-secondary text-sm flex-shrink-0 mr-2"
          >
            {currentSelectedProject === project.name
              ? "Continue"
              : "Open "}
          </button>
          <button
            type="button"
            onClick={handleTriggerBuild}
            className="btn btn-primary text-sm flex-shrink-0"
          >
            New Build
          </button>
          <svg
            className={`ml-3 h-5 w-5 text-gray-400 transform transition-transform duration-200 ${isExpanded ? "rotate-180" : ""
              }`}
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>

      {/* Expandable builds section */}
      {
        isExpanded && (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-gray-500">Builds</h3>
            </div>
            {project.builds.length === 0 ? (
              <p className="text-sm text-gray-500">No builds yet.</p>
            ) : (
              <ul className="divide-y divide-gray-200">
                {project.builds.map((build: Build) => (
                  <li key={build.id} className="py-3">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 mr-2">
                            {build.status === "completed" && build.url ? (
                              <a
                                href={build.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:text-blue-500"
                              >
                                Build #{build.id}
                              </a>
                            ) : (
                              <>Build #{build.id}</>
                            )}
                          </span>
                          <BuildStatusBadge status={build.status} />

                        </div>
                        <p className="text-xs text-gray-500">
                          {formatDate(build.createdAt)} {build.requested_by && (
                            <span className="text-xs italic text-gray-500 ml-2">
                              (by: {build.requested_by})
                            </span>
                          )}
                        </p>
                      </div>

                      {build.status === "completed" && build.url && (
                        <a
                          href={build.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-500"
                        >
                          View Build
                        </a>
                      )}
                      {(build.status === "queued" || build.status === "processing") && build.id != "1" && (
                        <button
                          type="button"
                          onClick={(e) => {
                            handleBuildKillClick(e, build);
                          }}
                          className="btn btn-danger text-sm flex-shrink-0">Remove</button>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        )
      }

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        title="Confirm New Build"
        message="Are you sure you want to create a new build for this project?"
        onConfirm={confirmBuild}
        onCancel={cancelBuild}
        additionalContent={
          <>
            <div className="mt-4">
              <label htmlFor="bakeLevelSlider" className="block text-lg font-medium text-gray-700">
                Bake Level: {["Off ❌ ", "Fast 🚀 ", "Balanced 😁 ", "High 😎 ", "Ultra 🤩 ",][bakeLevel]} ({bakeLevel})
              </label>
              <input
                type="range"
                id="bakeLevelSlider"
                name="bakeLevel"
                min="0"
                max="4"
                value={bakeLevel}
                onChange={(e) => setBakeLevel(parseInt(e.target.value, 10))}
                className="w-full h-5 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-600"
              />
            </div>
            <div className="mt-4">
              <label htmlFor="furnitureToggleSlider" className="block text-lg font-medium text-gray-700">
                Furniture: {["Off ❌", "On ✅ "][furnitureToggle]} ({furnitureToggle})
              </label>
              <input
                type="range"
                id="furnitureToggleSlider"
                name="furnitureToggle"
                min="0"
                max="1"
                value={furnitureToggle}
                onChange={(e) => setFurnitureToggle(parseInt(e.target.value, 10))}
                className="w-100 h-5 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-600"
              />
            </div>
          </>
        }
      />

      {/* Confirmation Kill Modal */}
      <ConfirmationModal
        isOpen={showConfirmKillModal}
        title="Confirm Cancel Build"
        message={`Are you sure you want to remove Build #${selectedBuild?.id}?`}
        onConfirm={() => confirmCancelBuild()}
        onCancel={cancelBuild}
      />
    </div >
  );
}
