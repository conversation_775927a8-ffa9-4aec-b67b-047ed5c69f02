import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchProjects, createBuild } from "../../api/api";
import ProjectCard from "./ProjectCard";
import { useAuth } from "../auth/AuthContext";
import { useGlobalDynaTag } from "../dynatag/DynaTagContext";
import { useNavigate } from "react-router-dom";
import { useState, useEffect, useMemo } from "react";

const SAVED_FILTERS_KEY = 'projects-saved-filters';

export default function ProjectsPage() {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useAuth();

  // Filter state
  const [filterText, setFilterText] = useState("");
  const [savedFilters, setSavedFilters] = useState<string[]>([]);

  // Load saved filters from localStorage on component mount
  useEffect(() => {
    const saved = localStorage.getItem(SAVED_FILTERS_KEY);
    if (saved) {
      try {
        setSavedFilters(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading saved filters:', error);
      }
    }
  }, []);

  // Use React Query to fetch projects
  const {
    data: projects = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["projects"],
    queryFn: () => fetchProjects({ user, isAuthenticated }),
  });
  const { currentSelectedProject, setCurrentSelectedProject } = useGlobalDynaTag();
  const navigate = useNavigate();

  // Filter projects based on prefix matching
  const filteredProjects = useMemo(() => {
    if (!filterText.trim()) {
      return projects;
    }
    const lowerFilter = filterText.toLowerCase();
    return projects.filter(project =>
      project.name.toLowerCase().startsWith(lowerFilter) ||
      project.description.toLowerCase().startsWith(lowerFilter)
    );
  }, [projects, filterText]);

  // Save filter to localStorage and update state
  const saveFilter = (filter: string) => {
    if (!filter.trim() || savedFilters.includes(filter)) {
      return;
    }

    const newFilters = [filter, ...savedFilters.filter(f => f !== filter)];
    setSavedFilters(newFilters);
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(newFilters));
  };

  // Remove filter from saved filters
  const removeFilter = (filter: string) => {
    const newFilters = savedFilters.filter(f => f !== filter);
    setSavedFilters(newFilters);
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(newFilters));
  };

  // Apply a saved filter
  const applyFilter = (filter: string) => {
    setFilterText(filter);
    // Move to front of recently used
    saveFilter(filter);
  };

  // Handle Enter key to save current filter
  const handleFilterKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && filterText.trim()) {
      saveFilter(filterText.trim());
    }
  };
  // Function to trigger a new build for a project
  const handleTriggerBuild = async (projectId: string, bakeLevel: number, furnitureToggle: number) => {
    try {
      // Call API to create a new build
      const newBuild = await createBuild(user, projectId, bakeLevel, furnitureToggle);

      // Update the cache with the new build
      queryClient.setQueryData(["projects"], (oldData: any) => {
        if (!oldData) return oldData;

        return oldData.map((project: any) => {
          if (project.id === projectId) {
            return {
              ...project,
              builds: [newBuild, ...project.builds],
            };
          }
          return project;
        });
      });
    } catch (err) {
      console.error("Error creating build:", err);
    }
  };

  // Function to force reload projects
  const handleReload = () => {
    refetch();
  };

  function handleOpen(projectId: string): void {
    console.log("handleOpen", projectId, currentSelectedProject);
    if (currentSelectedProject != projectId) {
      setCurrentSelectedProject(projectId);
    }
    navigate("/dashboard/dynatag");
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header - fixed at top */}
      <div className="p-1 pr-4 border-b border-gray-200 flex-shrink-0">
        <div className="sm:flex sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>

          <button
            onClick={handleReload}
            className="flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Reload
          </button>
        </div>

        {/* Filter Section */}
        <div className="mt-4">
          {/* Filter Input */}
          <div className="relative">
            <input
              type="text"
              placeholder="Filter projects by name or description..."
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              onKeyDown={handleFilterKeyDown}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            {filterText && (
              <button
                onClick={() => setFilterText("")}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* Saved Filters */}
          {savedFilters.length > 0 && (
            <div className="mt-2">
              <div className="flex flex-wrap gap-2">
                {savedFilters.map((filter) => (
                  <button
                    key={filter}
                    onClick={() => applyFilter(filter)}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {filter}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFilter(filter);
                      }}
                      className="ml-1 text-gray-400 hover:text-gray-600"
                    >
                      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto min-h-0">
        <div className="py-6">
          {/* Loading state */}
          {isLoading && (
            <div className="flex justify-center items-center h-64">
              Loading...
              <svg
                className="animate-spin h-8 w-8 text-blue-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          )}

          {/* Error state */}
          {isError && (
            <div className="bg-red-50 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-red-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Error loading projects
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      {error instanceof Error
                        ? error.message
                        : "Unknown error occurred"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Project list */}
          {!isLoading && !isError && (
            <div className="pl-2 pr-2 space-y-2">
              {projects.length === 0 ? (
                <div className="text-center py-8 bg-white rounded-lg shadow">
                  <p className="text-gray-500">No projects found.</p>
                </div>
              ) : (
                projects.map((project) => (
                  <ProjectCard
                    key={project.name}
                    project={project}
                    onTriggerBuild={handleTriggerBuild}
                    onOpen={handleOpen}
                  />
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
