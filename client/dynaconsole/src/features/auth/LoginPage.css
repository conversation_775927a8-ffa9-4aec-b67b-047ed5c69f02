/* Add or update these styles in your LoginPage.css file */

/* Update button colors to match home page blue */
.login-button, 
.primary-button,
.submit-button {
  background-color: #3498db;
  color: white;
  border: none;
  transition: background-color 0.3s ease;
}

.login-button:hover,
.primary-button:hover,
.submit-button:hover {
  background-color: #2980b9;
}

/* Update any focus states, borders, or text links to use the same blue */
input:focus,
select:focus,
textarea:focus {
  border-color: #3498db;
  outline-color: #3498db;
}

a,
.link,
.text-link {
  color: #3498db;
}

a:hover,
.link:hover,
.text-link:hover {
  color: #2980b9;
}

/* If there are any other blue elements or accents */
.accent-color,
.highlight,
.icon-highlight {
  color: #3498db;
}

.accent-border,
.highlight-border {
  border-color: #3498db;
}

/* If there's a header/title with a blue background */
.login-header {
  background-color: #3498db;
}

/* Add this to your LoginPage.css file if not already present */

.home-button-container {
  position: absolute;
  top: 2rem;
  left: 2rem;
}

.home-button {
  display: inline-flex;
  align-items: center;
  background-color: #f8f9fa;
  color: #3498db;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.home-button:hover {
  background-color: #3498db;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.home-button span {
  display: inline-flex;
  align-items: center;
}

/* Update the top-level structure for consistent layout */
.login-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.login-content {
  flex: 1;
  padding: 3rem 0;
  background-color: #f5f7fa;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.login-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
}

.login-card h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background-color: #2980b9;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 0.5rem;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 4px;
} 