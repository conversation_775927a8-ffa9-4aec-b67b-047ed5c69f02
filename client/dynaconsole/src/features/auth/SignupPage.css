/* Update the top-level structure for consistent layout */
.signup-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.signup-content {
  flex: 1;
  padding: 3rem 0;
  background-color: #f5f7fa;
}

.signup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.signup-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
}

.signup-card h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c3e50;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.submit-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #2980b9;
}

.success-message {
  text-align: center;
  padding: 2rem 0;
}

.check-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background-color: #2ecc71;
  color: white;
  font-size: 2.5rem;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
}

.success-message h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.success-message p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}

.confetti {
  font-size: 2rem;
  text-align: center;
}

.home-button-container {
  position: absolute;
  top: 2rem;
  left: 2rem;
}

.home-button {
  display: inline-flex;
  align-items: center;
  background-color: #f8f9fa;
  color: #3498db;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.home-button:hover {
  background-color: #3498db;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.home-button span {
  display: inline-flex;
  align-items: center;
}

/* Email validation error styles */
input.error {
  border-color: #e74c3c !important;
}

.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

/* Phone input styling */
.phone-input-container {
  width: 100%;
}

.phone-input {
  width: 100% !important;
  padding: 0.75rem !important;
  padding-left: 50px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 1rem !important;
  height: auto !important;
}

.phone-dropdown-button {
  border: 1px solid #ddd !important;
  border-radius: 4px 0 0 4px !important;
  border-right: none !important;
  background-color: #f8f9fa !important;
}

.phone-dropdown {
  margin: 0 !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

.phone-dropdown .country {
  padding: 10px !important;
}

.phone-dropdown .country:hover {
  background-color: #f1f1f1 !important;
}

.phone-dropdown .search-box {
  padding: 10px !important;
  margin: 0 !important;
}

.phone-dropdown .search-box input {
  width: 100% !important;
  padding: 8px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
} 