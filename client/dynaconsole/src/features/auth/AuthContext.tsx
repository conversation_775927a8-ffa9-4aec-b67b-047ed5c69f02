import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
  useRef,
} from "react";
import { get_current_user, login_api, logout_user } from "../../api/api";
import { UserLoginResponse } from "../../api/types";
import { usePostHog } from "posthog-js/react";

interface AuthContextType {
  user: UserLoginResponse | null;
  isAuthenticated: boolean;
  check_if_user_is_logged_in: () => Promise<boolean>;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
}

// Create context with a default undefined value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserLoginResponse | null>(null);
  const intervalRef = useRef<number | null>(null);
  const posthog = usePostHog();
  // Set up the token refresh interval
  const setupTokenRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (user) {
      const exp_at_ms = user.token_expiry * 1000;
      const exp_in_ms = exp_at_ms - Date.now();
      const refresh_interval = exp_in_ms / 4;
      intervalRef.current = window.setInterval(
        check_if_user_is_logged_in,
        refresh_interval
      );
      console.log("Token refresh interval set up : ", refresh_interval);
    } else {
      console.log("No user found, wont refresh token");
    }
  };

  // Clear the token refresh interval
  const clearTokenRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      console.log("Token refresh interval cleared");
    }
  };

  // Mock login function - in a real app, this would make an API call
  const login = async (email: string, password: string): Promise<boolean> => {
    const user_response = await login_api(email, password);
    if (user_response) {
      setUser(user_response);
      return true;
    }
    return false;
  };

  const check_if_user_is_logged_in = async (): Promise<boolean> => {
    console.log("Checking if user is logged in");
    const user_response = await get_current_user();
    if (user_response) {
      setUser(user_response);
      return true;
    }
    return false;
  };

  const logout = async () => {
    clearTokenRefresh(); // Stop refreshing token on logout
    await logout_user();
    setUser(null);
  };

  // Set up initial token refresh if a user exists
  useEffect(() => {
    if (user) {
      //console.log("Identifying user with posthog" + posthog);
      posthog?.identify(user.email, {
        email: user.email,
        account_id: user.account_id,
      });
      posthog?.group("account", user.account_id);
      posthog?.capture("user_logged_in");
      setupTokenRefresh();
    } else {
      console.log("Resetting posthog");
      posthog?.reset();
      clearTokenRefresh();
    }

    // Clean up interval on unmount
    return () => {
      clearTokenRefresh();
    };
  }, [user, user?.access_token]); // Refresh setup when access token changes

  // Check if user is logged in on mount
  useEffect(() => {
    console.log("Checking if user is logged in");
    check_if_user_is_logged_in();
  }, []);

  const value = {
    user,
    isAuthenticated: !!user,
    check_if_user_is_logged_in,
    login,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
