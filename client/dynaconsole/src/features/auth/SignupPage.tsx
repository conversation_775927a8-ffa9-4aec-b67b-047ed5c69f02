import React, { useState } from 'react';
import { motion } from 'framer-motion'; // You'll need to install framer-motion
import { Link } from 'react-router-dom';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import Header from '../home/<USER>/Header';
import Footer from '../home/<USER>/Footer';
import './SignupPage.css';

const SignupPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [emailError, setEmailError] = useState('');

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    
    if (value && !validateEmail(value)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate email before submission
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }
    
    // Here you would normally send the data to your backend
    setSubmitted(true);
  };

  return (
    <div className="signup-page">
      <Header />
      
      <main className="signup-content">
        <div className="signup-container">
          <div className="home-button-container">
            <Link to="/" className="home-button">
              <span>← Home</span>
            </Link>
          </div>
          
          <div className="signup-card">
            <h1>Sign Up</h1>
            
            {!submitted ? (
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <label htmlFor="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={handleEmailChange}
                    className={emailError ? 'error' : ''}
                    required
                  />
                  {emailError && <div className="error-message">{emailError}</div>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">Phone Number</label>
                  <PhoneInput
                    country={'in'}
                    value={phone}
                    onChange={(phone) => setPhone(phone)}
                    inputProps={{
                      name: 'phone',
                      required: true,
                      autoFocus: false
                    }}
                    countryCodeEditable={false}
                    enableSearch={true}
                    disableSearchIcon={true}
                    containerClass="phone-input-container"
                    inputClass="phone-input"
                    buttonClass="phone-dropdown-button"
                    dropdownClass="phone-dropdown"
                  />
                </div>
                
                <button type="submit" className="submit-btn">Submit</button>
              </form>
            ) : (
              <motion.div 
                className="success-message"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  className="check-icon"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1, rotate: 360 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 260, damping: 20 }}
                >
                  ✓
                </motion.div>
                <h2>Thank You!</h2>
                <p>We have received your request, and we will soon get in touch with you!</p>
                <motion.div
                  className="confetti"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  🎉
                </motion.div>
              </motion.div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SignupPage; 