.main-header {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.main-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo a {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-nav ul li {
  margin-left: 1.5rem;
}

.main-nav ul li a {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.main-nav ul li a:hover {
  color: #3498db;
}

@media (max-width: 768px) {
  .main-header .container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .main-nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .main-nav ul li {
    margin: 0.5rem;
  }
} 