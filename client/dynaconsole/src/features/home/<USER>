import React from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import './HomePage.css';

const HomePage: React.FC = () => {
  return (
    <div className="home-page">
      <Header />
      
      <main className="home-body">
        <div className="container">
          <h1>Welcome to SpaceViz.ai</h1>
          <p>Your all-in-one solution for project management and more</p>
          
          <div className="cta-buttons">
            <button className="primary-btn">Get Started</button>
            <button className="secondary-btn">Learn More</button>
          </div>
          
          <div className="features">
            <div className="feature-card">
              <h3>Feature 1</h3>
              <p>Description of feature 1</p>
            </div>
            <div className="feature-card">
              <h3>Feature 2</h3>
              <p>Description of feature 2</p>
            </div>
            <div className="feature-card">
              <h3>Feature 3</h3>
              <p>Description of feature 3</p>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default HomePage; 