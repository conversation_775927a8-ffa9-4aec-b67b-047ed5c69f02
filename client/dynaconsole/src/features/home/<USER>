.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.home-body {
  flex: 1;
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.primary-btn, .secondary-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-btn {
  background-color: #3498db;
  color: white;
  border: none;
}

.secondary-btn {
  background-color: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  padding: 1.5rem;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
} 