import React from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import './DocumentationPage.css';

const DocumentationPage: React.FC = () => {
  return (
    <div className="documentation-page">
      <Header />
      
      <main className="documentation-content">
        <div className="container">
          <h1>Documentation</h1>
          
          <div className="docs-layout">
            <aside className="docs-sidebar">
              <h3>Getting Started</h3>
              <ul>
                <li><a href="#installation">Installation</a></li>
                <li><a href="#quickstart">Quick Start</a></li>
                <li><a href="#configuration">Configuration</a></li>
              </ul>
              
              <h3>Features</h3>
              <ul>
                <li><a href="#feature1">Feature 1</a></li>
                <li><a href="#feature2">Feature 2</a></li>
                <li><a href="#feature3">Feature 3</a></li>
              </ul>
              
              <h3>API Reference</h3>
              <ul>
                <li><a href="#api1">API Endpoint 1</a></li>
                <li><a href="#api2">API Endpoint 2</a></li>
              </ul>
            </aside>
            
            <div className="docs-content">
              <section id="installation">
                <h2>Installation</h2>
                <p>Follow these steps to install our product:</p>
                <pre><code>npm install dynaconsole</code></pre>
              </section>
              
              <section id="quickstart">
                <h2>Quick Start</h2>
                <p>Get up and running in minutes with these simple steps:</p>
                <ol>
                  <li>Install the package</li>
                  <li>Configure your settings</li>
                  <li>Start using the product</li>
                </ol>
              </section>
              
              <section id="configuration">
                <h2>Configuration</h2>
                <p>Configure the product to suit your needs:</p>
                <pre><code>{`
// Example configuration
{
  "apiKey": "your-api-key",
  "endpoint": "https://api.example.com",
  "options": {
    "timeout": 5000,
    "retries": 3
  }
}
                `}</code></pre>
              </section>
              
              {/* Additional sections would go here */}
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default DocumentationPage; 