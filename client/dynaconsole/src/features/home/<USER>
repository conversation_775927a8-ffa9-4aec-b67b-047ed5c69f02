import React from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import './ProductPage.css';

const ProductPage: React.FC = () => {
  return (
    <div className="product-page">
      <Header />
      
      <main className="product-content">
        <div className="container">
          <h1>Our Product</h1>
          
          <section className="product-section">
            <h2>Features</h2>
            <div className="feature-grid">
              <div className="feature-item">
                <h3>Feature 1</h3>
                <p>Detailed description of feature 1 and its benefits.</p>
              </div>
              <div className="feature-item">
                <h3>Feature 2</h3>
                <p>Detailed description of feature 2 and its benefits.</p>
              </div>
              <div className="feature-item">
                <h3>Feature 3</h3>
                <p>Detailed description of feature 3 and its benefits.</p>
              </div>
            </div>
          </section>
          
          <section className="product-section">
            <h2>Pricing</h2>
            <div className="pricing-options">
              <div className="pricing-card">
                <h3>Basic</h3>
                <p className="price">$9.99/month</p>
                <ul>
                  <li>Feature 1</li>
                  <li>Feature 2</li>
                  <li>Feature 3</li>
                </ul>
                <button>Get Started</button>
              </div>
              
              <div className="pricing-card featured">
                <h3>Pro</h3>
                <p className="price">$19.99/month</p>
                <ul>
                  <li>Everything in Basic</li>
                  <li>Premium Feature 1</li>
                  <li>Premium Feature 2</li>
                </ul>
                <button>Get Started</button>
              </div>
              
              <div className="pricing-card">
                <h3>Enterprise</h3>
                <p className="price">Contact Us</p>
                <ul>
                  <li>Everything in Pro</li>
                  <li>Custom Solutions</li>
                  <li>Dedicated Support</li>
                </ul>
                <button>Contact Sales</button>
              </div>
            </div>
          </section>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ProductPage; 