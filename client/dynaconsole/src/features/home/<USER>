.documentation-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.documentation-content {
  flex: 1;
  padding: 3rem 0;
}

.docs-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.docs-sidebar {
  padding-right: 1.5rem;
  border-right: 1px solid #e1e1e1;
}

.docs-sidebar h3 {
  font-size: 1.2rem;
  margin: 1.5rem 0 0.75rem;
}

.docs-sidebar h3:first-child {
  margin-top: 0;
}

.docs-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem;
}

.docs-sidebar ul li {
  margin-bottom: 0.5rem;
}

.docs-sidebar ul li a {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
  display: block;
  padding: 0.25rem 0;
}

.docs-sidebar ul li a:hover {
  color: #3498db;
}

.docs-content section {
  margin-bottom: 3rem;
}

.docs-content h2 {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e1e1e1;
}

.docs-content pre {
  background-color: #f6f8fa;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.docs-content code {
  font-family: monospace;
}

@media (max-width: 768px) {
  .docs-layout {
    grid-template-columns: 1fr;
  }
  
  .docs-sidebar {
    border-right: none;
    border-bottom: 1px solid #e1e1e1;
    padding-right: 0;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
  }
} 