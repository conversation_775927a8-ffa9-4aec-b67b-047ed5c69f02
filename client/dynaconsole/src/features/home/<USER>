.product-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.product-content {
  flex: 1;
  padding: 3rem 0;
}

.product-section {
  margin-bottom: 4rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-item {
  padding: 1.5rem;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pricing-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.pricing-card {
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-5px);
}

.pricing-card.featured {
  border: 2px solid #3498db;
  transform: scale(1.05);
}

.pricing-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.pricing-card .price {
  font-size: 2rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 1.5rem;
}

.pricing-card ul {
  text-align: left;
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

.pricing-card ul li {
  margin-bottom: 0.5rem;
}

.pricing-card button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pricing-card button:hover {
  background-color: #2980b9;
} 