import { BuildStatus } from '../../api/types';

interface BuildStatusBadgeProps {
  status: BuildStatus;
}

export default function BuildStatusBadge({ status }: BuildStatusBadgeProps) {
  const statusConfig = {
    'queued': {
      label: 'In Queue',
      className: 'bg-yellow-100 text-yellow-800',
    },
    'processing': {
      label: 'Processing',
      className: 'bg-blue-100 text-blue-800',
    },
    'completed': {
      label: 'Complete',
      className: 'bg-green-100 text-green-800',
    },
    'failed': {
      label: 'Failed',
      className: 'bg-red-100 text-red-800',
    },
    'cancelled': {
      label: 'Cancelled',
      className: 'bg-gray-100 text-gray-800',
    },
    'manual': {
      label: 'Manual - In Progress',
      className: 'bg-purple-100 text-purple-800',
    },
  };

  const { label, className } = statusConfig[status as keyof typeof statusConfig];

  return (
    <div>
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${className}`}>
        {label}
      </span>
    </div>
  );
} 