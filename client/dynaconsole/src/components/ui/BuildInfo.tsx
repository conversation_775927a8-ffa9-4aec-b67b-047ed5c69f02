import { useEffect, useState } from "react";

function BuildInfo() {
    const [buildData, setBuildData] = useState<any>({});
    const [error, setError] = useState<any>(null);

    useEffect(() => {
        fetch("/build.json")
            .then((res) => {
                if (res.status === 404) {
                    throw new Error("build.json not found");
                }
                if (!res.ok) {
                    throw new Error("Failed to fetch build.json");
                }
                return res.json();
            })
            .then((data) => {
                try {
                    buildData.react_ts = data.build_human
                    setBuildData(buildData);
                } catch (e) {
                    setError("Failed to parse build.json");
                }
            })
            .catch((err) => setError(err.message));
        fetch("/build-dyntag.json")
            .then((res) => {
                if (res.status === 404) {
                    throw new Error("build.json not found");
                }
                if (!res.ok) {
                    throw new Error("Failed to fetch build.json");
                }
                return res.json();
            })
            .then((data) => {

                try {
                    buildData.dt_ts = data.timestamp;
                    var cs_comps = data.changeset.split("@");
                    buildData.dt_branch = cs_comps[0];
                    buildData.dt_cs = cs_comps[3];
                    setBuildData(buildData);
                } catch (e) {
                    setError("Failed to parse build-dyntag.json");
                }

            })
            .catch((err) => setError(err.message));
    }, []);

    if (error) {
        return <div style={{ color: "gray" }}>⚠️ {error}</div>;
    }

    if (!buildData) {
        return <div>Loading build info...</div>;
    }

    return (
        <div>
            <h3 className="text-[0.7rem]">Build Info</h3>
            <pre className="text-[0.4rem] text-gray-500">{JSON.stringify(buildData, null, 2)}</pre>
        </div>
    );
}

export default BuildInfo;
