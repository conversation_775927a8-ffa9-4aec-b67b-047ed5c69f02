import { useEffect, useRef, ReactNode } from 'react';
import { createPortal } from 'react-dom';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
}

export default function Modal({ isOpen, onClose, children }: ModalProps) {
  const modalRoot = document.getElementById('modal-root');
  const modalElement = useRef<HTMLDivElement | null>(null);
  
  if (!modalElement.current) {
    modalElement.current = document.createElement('div');
  }
  
  useEffect(() => {
    if (isOpen && modalRoot) {
      modalRoot.appendChild(modalElement.current as HTMLDivElement);
      return () => {
        modalRoot.removeChild(modalElement.current as HTMLDivElement);
      };
    }
  }, [isOpen, modalRoot]);
  
  if (!isOpen) return null;
  
  return createPortal(
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-gray-600 bg-opacity-50 z-20"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal content */}
      <div className="fixed inset-0 z-30 overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4 text-center">
          {children}
        </div>
      </div>
    </>,
    modalElement.current
  );
} 