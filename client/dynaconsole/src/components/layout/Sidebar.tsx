import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../features/auth/AuthContext";
import { useGlobalDynaTag } from "../../features/dynatag/DynaTagContext";
import BuildInfo from "../ui/BuildInfo";

interface SidebarProps {
  onClose?: () => void;
}

export default function Sidebar({ onClose }: SidebarProps) {
  const { user, logout } = useAuth();
  const {currentSelectedProject} = useGlobalDynaTag();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname.startsWith(path);
  };
  

  const handleNavClick = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <aside className="w-64 h-full bg-white border-r flex flex-col">
      {/* Logo */}
      <div className="h-24 flex flex-col justify-center px-4 border-b">
        <span className="text-xl font-bold text-blue-600">SpaceViz.ai</span>
        <div className="flex flex-col text-xs text-gray-600 mt-1">
          <span>Account: {user?.account_id}</span>
          <span>Email: {user?.email}</span>

          <span className="font-bold">{currentSelectedProject}</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <Link
          to="/dashboard/projects"
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            isActive("/dashboard/projects")
              ? "bg-blue-100 text-blue-800"
              : "text-gray-700 hover:bg-gray-100"
          }`}
          onClick={handleNavClick}
        >
          <svg
            className="mr-3 h-5 w-5 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
            />
          </svg>
          Projects
        </Link>
        <Link
          to="/dashboard/dynatag"
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            isActive("/dashboard/dynatag")
              ? "bg-blue-100 text-blue-800"
              : "text-gray-700 hover:bg-gray-100"
          }`}
          onClick={handleNavClick}
        >
          <svg
            className="mr-3 h-5 w-5 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"
            />
          </svg>
          DynaTag
        </Link>
        <Link
          to="/dashboard/settings"
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            isActive("/dashboard/settings")
              ? "bg-blue-100 text-blue-800"
              : "text-gray-700 hover:bg-gray-100"
          }`}
          onClick={handleNavClick}
        >
          <svg
            className="mr-3 h-5 w-5 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
          Settings
        </Link>
      </nav>

      {/* Logout button */}
      <div className="p-4 border-t">
        <BuildInfo />
        <button
          onClick={logout}
          className="btn btn-danger w-full flex items-center justify-center"
        >
          <svg
            className="mr-2 h-5 w-5 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
          Logout
        </button>
      </div>
    </aside>
  );
}
