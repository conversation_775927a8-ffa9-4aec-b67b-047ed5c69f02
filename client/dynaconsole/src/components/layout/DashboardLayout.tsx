import { useState, useEffect } from "react";
import { Outlet, Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../features/auth/AuthContext";
import Sidebar from "./Sidebar";

import { usePostHog } from "posthog-js/react";
import DynaTagWebGL from "../../features/dynatag/DynaTagWebGL";
import { useGlobalDynaTag } from "../../features/dynatag/DynaTagContext";

export default function DashboardLayout() {
  const posthog = usePostHog();
  const { isAuthenticated } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const isDynaTagRoute = location.pathname === "/dashboard/dynatag";
  const { sendMessage } = useGlobalDynaTag();

  // Close sidebar when window resizes to desktop size
  useEffect(() => {
    console.log(
      "posthog feature flag",
      posthog.getFeatureFlagPayload("test-flag")
    );
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Tell Unity about route changes to manage keyboard focus
  useEffect(() => {
    console.log('Route changed, isDynaTagRoute:', isDynaTagRoute);

    // Small delay to ensure Unity is ready
    const timer = setTimeout(() => {
      try {
        if (sendMessage) {
          // Tell Unity whether it should capture keyboard input
          sendMessage("ReactBridge", "RU_SetKeyboardCapture", isDynaTagRoute ? "true" : "false");
          console.log('Sent keyboard capture message to Unity:', isDynaTagRoute);
        }
      } catch (error) {
        console.log('Could not send message to Unity (might not be loaded yet):', error);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isDynaTagRoute, sendMessage]);

  // Global keyboard event handler to prevent Unity from capturing when not on dynatag route
  useEffect(() => {
    if (isDynaTagRoute) return; // Let Unity handle keyboard when on dynatag route

    const handleKeyDown = (e: KeyboardEvent) => {
      // If we're focused on an input element, don't let Unity steal the event
      const target = e.target as HTMLElement;
      if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable)) {
        e.stopImmediatePropagation();
        console.log('Prevented Unity from capturing keyboard event for input:', e.key);
      }
    };

    // Add event listener with capture=true to intercept before Unity gets it
    document.addEventListener('keydown', handleKeyDown, { capture: true });
    document.addEventListener('keyup', handleKeyDown, { capture: true });
    document.addEventListener('keypress', handleKeyDown, { capture: true });

    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
      document.removeEventListener('keyup', handleKeyDown, { capture: true });
      document.removeEventListener('keypress', handleKeyDown, { capture: true });
    };
  }, [isDynaTagRoute]);

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 transition duration-300 ease-in-out transform lg:hidden ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <Sidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="w-64">
          <Sidebar />
        </div>
      </div>

      {/* Main content */}
      <main className="flex-1 flex flex-col h-screen overflow-hidden">
        {/* Mobile top bar */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between bg-white px-4 py-2 border-b">
            <button
              type="button"
              className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <div className="flex-1 flex justify-center">
              <h1 className="text-xl font-bold text-blue-600">SpaceViz.ai </h1>
            </div>
            <div className="w-8"></div> {/* Spacer for alignment */}
          </div>
        </div>

        {/* Persistent WebGL component (always loaded, display toggled) */}
        <div
          className={`w-full h-full flex-1 ${!isDynaTagRoute ? "hidden" : ""}`}
          style={{
            display: isDynaTagRoute ? "block" : "none",
            width: "100%",
            height: "100vh", // Full viewport height
            // position: isDynaTagRoute ? "absolute" : "relative",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <DynaTagWebGL />
        </div>

        {/* Regular routes rendered through Outlet */}
        <div
          className={`${isDynaTagRoute ? "hidden" : "flex-1 overflow-hidden"}`}
        >
          <div className="h-full overflow-auto">
            <div className="h-full">
              <Outlet />
            </div>
          </div>
        </div>

        {/* Persistent iframe */}
        {/*
          <div 
            className={`w-full h-full flex-1 ${!isDynaTagRoute ? 'hidden' : ''}`}
            style={{ display: isDynaTagRoute ? 'block' : 'none' }}
          >
            <iframe
              src="https://app.spaceviz.ai/"
              className="w-full h-full border-0"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                margin: 0,
                padding: 0,
              }}
              title="DynaTag"
              allow="fullscreen"
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
            />
          </div>
          */}
      </main>
    </div>
  );
}
