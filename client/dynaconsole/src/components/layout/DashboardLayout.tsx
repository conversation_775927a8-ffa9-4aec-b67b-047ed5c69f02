import { useState, useEffect } from "react";
import { Outlet, Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../features/auth/AuthContext";
import Sidebar from "./Sidebar";

import { usePostHog } from "posthog-js/react";
import DynaTagWebGL from "../../features/dynatag/DynaTagWebGL";
import { useGlobalDynaTag } from "../../features/dynatag/DynaTagContext";

export default function DashboardLayout() {
  const posthog = usePostHog();
  const { isAuthenticated } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const isDynaTagRoute = location.pathname === "/dashboard/dynatag";
  const { sendMessage } = useGlobalDynaTag();

  // Close sidebar when window resizes to desktop size
  useEffect(() => {
    console.log(
      "posthog feature flag",
      posthog.getFeatureFlagPayload("test-flag")
    );
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);



  // Prevent Unity from hijacking keyboard events for input elements
  useEffect(() => {
    const handleKeyEvent = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;

      // Skip if this is our own dispatched event to prevent infinite loop
      if ((e as any).__isRedispatched) {
        return;
      }

      // If the event is targeting an input or textarea, stop it from reaching Unity
      if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable)) {
        e.stopImmediatePropagation();
        e.preventDefault();
        console.log('Blocked Unity from capturing keyboard event for input:', e.key);

        // For keydown events, manually update the input value for printable characters
        if (e.type === 'keydown' && target.tagName === 'INPUT') {
          const input = target as HTMLInputElement;
          if (e.key.length === 1 && !e.ctrlKey && !e.altKey && !e.metaKey) {
            // Handle printable characters
            const start = input.selectionStart || 0;
            const end = input.selectionEnd || 0;
            const currentValue = input.value;
            const newValue = currentValue.slice(0, start) + e.key + currentValue.slice(end);

            input.value = newValue;
            input.setSelectionRange(start + 1, start + 1);

            // Trigger React's onChange event properly
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;
            if (nativeInputValueSetter) {
              nativeInputValueSetter.call(input, newValue);
            }

            const inputEvent = new Event('input', { bubbles: true });
            input.dispatchEvent(inputEvent);
          } else if (e.key === 'Backspace') {
            // Handle backspace
            const start = input.selectionStart || 0;
            const end = input.selectionEnd || 0;
            const currentValue = input.value;

            if (start === end && start > 0) {
              // Delete one character before cursor
              const newValue = currentValue.slice(0, start - 1) + currentValue.slice(start);
              input.value = newValue;
              input.setSelectionRange(start - 1, start - 1);
            } else if (start !== end) {
              // Delete selected text
              const newValue = currentValue.slice(0, start) + currentValue.slice(end);
              input.value = newValue;
              input.setSelectionRange(start, start);
            }

            // Trigger React's onChange event properly
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;
            if (nativeInputValueSetter) {
              nativeInputValueSetter.call(input, input.value);
            }

            const inputEvent = new Event('input', { bubbles: true });
            input.dispatchEvent(inputEvent);
          }
        }
      }
    };

    // Add global event listeners with capture=true to intercept before Unity
    document.addEventListener('keydown', handleKeyEvent, { capture: true });

    return () => {
      document.removeEventListener('keydown', handleKeyEvent, { capture: true });
    };
  }, []);

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 transition duration-300 ease-in-out transform lg:hidden ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <Sidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="w-64">
          <Sidebar />
        </div>
      </div>

      {/* Main content */}
      <main className="flex-1 flex flex-col h-screen overflow-hidden">
        {/* Mobile top bar */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between bg-white px-4 py-2 border-b">
            <button
              type="button"
              className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <div className="flex-1 flex justify-center">
              <h1 className="text-xl font-bold text-blue-600">SpaceViz.ai </h1>
            </div>
            <div className="w-8"></div> {/* Spacer for alignment */}
          </div>
        </div>

        {/* Persistent WebGL component (always loaded, display toggled) */}
        <div
          className={`w-full h-full flex-1 ${!isDynaTagRoute ? "hidden" : ""}`}
          style={{
            display: isDynaTagRoute ? "block" : "none",
            width: "100%",
            height: "100vh", // Full viewport height
            // position: isDynaTagRoute ? "absolute" : "relative",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <DynaTagWebGL />
        </div>

        {/* Regular routes rendered through Outlet */}
        <div
          className={`${isDynaTagRoute ? "hidden" : "flex-1 overflow-hidden"}`}
        >
          <div className="h-full overflow-auto">
            <div className="h-full">
              <Outlet />
            </div>
          </div>
        </div>

        {/* Persistent iframe */}
        {/*
          <div 
            className={`w-full h-full flex-1 ${!isDynaTagRoute ? 'hidden' : ''}`}
            style={{ display: isDynaTagRoute ? 'block' : 'none' }}
          >
            <iframe
              src="https://app.spaceviz.ai/"
              className="w-full h-full border-0"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                margin: 0,
                padding: 0,
              }}
              title="DynaTag"
              allow="fullscreen"
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
            />
          </div>
          */}
      </main>
    </div>
  );
}
