
import { Unity, useUnityContext } from "react-unity-webgl";
import { UserLoginResponse } from "./api/types";

const WEBGL_ROOT = "./WebDynaTag/Build";
const WEBGL_FILES_PREFIX = `${WEBGL_ROOT}/WebDynaTag`;
const BR_EXTENSION = ""; //".br";

export default function TestWebGL() {
  const { unityProvider, sendMessage } = useUnityContext({
    loaderUrl: `${WEBGL_FILES_PREFIX}.loader.js`,
    dataUrl: `${WEBGL_FILES_PREFIX}.data${BR_EXTENSION}`,
    frameworkUrl: `${WEBGL_FILES_PREFIX}.framework.js${BR_EXTENSION}`,
    codeUrl: `${WEBGL_FILES_PREFIX}.wasm${BR_EXTENSION}`,
  });

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <button
        onClick={() => {
          const user: UserLoginResponse = {
            email: "a@a.a",
            access_token: "**********",
            token_expiry: **********,
            account_id: "UNKNOWN_ACCOUNT",
          };
          console.log("Sending message to Unity");
          sendMessage(
            "ReactBridge",
            "RU_BaseUrlReceived",
            "https://api.spaceviz.ai"
          );

          console.log("Sending auth info to Unity:", user);
          sendMessage(
            "ReactBridge",
            "RU_AuthInfoReceived",
            JSON.stringify(user)
          );
          sendMessage(
            "ReactBridge",
            "RU_OpenProject",
            "Shreya House MHX_Sagar_StructInfo_Tagging_WIP_8.mhx"
          );
          console.log("Message sent to Unity");
        }}
      >
        Open2 Project2
      </button>
      <Unity
        unityProvider={unityProvider}
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
}
