import { HashRouter, Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "./features/auth/AuthContext";
import LoginPage from "./features/auth/LoginPage";
import DashboardLayout from "./components/layout/DashboardLayout";
import ProjectsPage from "./features/projects/ProjectsPage";
import SettingsPage from "./features/settings/SettingsPage";
import HomePage from "./features/home/<USER>";
import ProductPage from "./features/home/<USER>";
import DocumentationPage from "./features/home/<USER>";
import SignupPage from "./features/auth/SignupPage";
import { DynaTagProvider } from "./features/dynatag/DynaTagContext";

// Create a client
const queryClient = new QueryClient();

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <DynaTagProvider>
          <HashRouter>
            <Routes>
              {/* Home routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/product" element={<ProductPage />} />
              <Route path="/documentation" element={<DocumentationPage />} />

              {/* Auth routes */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />

              {/* Dashboard routes */}

              <Route path="/dashboard" element={<DashboardLayout />}>
                <Route path="projects" element={<ProjectsPage />} />
                <Route path="settings" element={<SettingsPage />} />
                <Route path="dynatag" element={null} />

                <Route
                  path=""
                  element={<Navigate to="/dashboard/projects" replace />}
                />
              </Route>

              {/* Default redirect */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </HashRouter>
        </DynaTagProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
