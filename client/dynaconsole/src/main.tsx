import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

import { PostHogProvider } from "posthog-js/react";
import App from "./App";

const options = {
  api_host: import.meta.env.VITE_PUBLIC_POSTHOG_HOST,
};
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <PostHogProvider
      apiKey={import.meta.env.VITE_PUBLIC_POSTHOG_KEY}
      options={options}
    >
      <App />
    </PostHogProvider>
  </StrictMode>
);
