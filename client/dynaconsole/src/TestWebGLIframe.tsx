import { useEffect, useRef } from "react";

export default function TestWebGLIframe() {
  const iframeRef = useRef<HTMLIFrameElement>(null);


  // Function to send message to Unity
  const sendMessageToUnity = (functionName: string, parameter: string) => {
    if (!iframeRef.current || !iframeRef.current.contentWindow) return;

    try {
      // Access Unity instance through the iframe's window
      const unityInstance = (iframeRef.current.contentWindow as any)
        .gameInstance;
      if (unityInstance && typeof unityInstance.SendMessage === "function") {
        unityInstance.SendMessage("ReactBridge", functionName, parameter);
        console.log(`Message sent to Unity: ${functionName}(${parameter})`);
      } else {
        console.warn("Unity instance not found or SendMessage not available");
      }
    } catch (error) {
      console.error("Error sending message to Unity:", error);
    }
  };

  const open_project = function() {
    sendMessageToUnity("RU_OpenProject", "Shreya House MHX_Sagar_StructInfo_Tagging_WIP_6.mhx");
  }

  useEffect(() => {
    // Listener for message events from the iframe
    const handleMessageFromIframe = (event: MessageEvent) => {
      console.log("Received from Unity:", event);
      if (event.data && event.data.type === "UR_OnDyntagAppLoad") {
        console.log("Unity WebGL Loaded via iframe message!");
        console.log("iframe ref", iframeRef.current);
        console.log("iframe ref2", iframeRef.current?.contentWindow);
        console.log("iframe ref3", (iframeRef.current?.contentWindow as any).gameInstance);
        console.log("iframe ref4", (iframeRef.current?.contentWindow as any).gameInstance.sendMessage);

        // Unity is now loaded and ready
        sendMessageToUnity(
          "RU_BaseUrlReceived",
          import.meta.env.VITE_API_DYNASERVER
        );

        const authInfo = {
          email: "a@a.a",
          access_token: "sample-token",
          token_expiry: Date.now() + 3600000,
          account_id: "UNKNOWN_ACCOUNT",
        };
        sendMessageToUnity("RU_AuthInfoReceived", JSON.stringify(authInfo));
      }
    };

    // Add message event listener to receive messages from the iframe
    window.addEventListener("message", handleMessageFromIframe);

    // Cleanup on unmount
    return () => {
      window.removeEventListener("message", handleMessageFromIframe);
    };
  }, []);

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <div>
        <button onClick={() => open_project()}>
          Send Base URL
        </button>
      </div>
      <iframe
        ref={iframeRef}
        src="./WebDynaTag/index.html"
        style={{ width: "100%", height: "100%", border: "none" }}
        title="Unity WebGL"
      />
    </div>
  );
}
