@import "tailwindcss";
@tailwind utilities;

/* Base styles */
html, body, #root {
  height: 100%;
  width: 100%;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
}

/* Custom utility classes */
@reference "tailwindcss";

@utility btn {
  padding-inline: 1rem;
  padding-block: 0.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition-property: color, background-color, border-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-white), 0 0 0 4px var(--tw-ring-color);
  }
}

@utility btn-primary {
  background-color: var(--color-blue-600);
  color: var(--color-white);
  &:hover {
    background-color: var(--color-blue-700);
  }
  --tw-ring-color: var(--color-blue-500);
}

@utility btn-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-800);
  &:hover {
    background-color: var(--color-gray-300);
  }
  --tw-ring-color: var(--color-gray-500);
}

@utility btn-danger {
  background-color: var(--color-red-100);
  color: var(--color-red-700);
  &:hover {
    background-color: var(--color-red-200);
  }
  --tw-ring-color: var(--color-red-500);
}

@utility form-input {
  display: block;
  width: 100%;
  padding-inline: 0.75rem;
  padding-block: 0.5rem;
  border: 1px solid var(--color-gray-300);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  &:focus {
    outline: none;
    border-color: var(--color-blue-500);
    box-shadow: 0 0 0 1px var(--color-blue-500);
  }
  font-size: 0.875rem;
}

@utility form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: 0.25rem;
}

@utility card {
  background-color: var(--color-white);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  overflow: hidden;
}

/* Override default styles */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0;
}

/* Make buttons look consistent */
button {
  cursor: pointer;
  white-space: nowrap;
}

/* Fix button and text overflow */
button, a {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Make inputs consistent */
input, select, textarea {
  font-family: inherit;
}

/* Fix flex layout issues */
.flex {
  min-width: 0;
  min-height: 0;
}

/* Ensure text doesn't overflow containers */
.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* Fix for mobile layout */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@theme {
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-500: #ef4444;
  --color-red-700: #b91c1c;
  
  --color-white: #ffffff;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.container-unity {
  
  /* The container determains the size. */
  width: 100%;
  height: 100%;
}

.container-unity > .loading-overlay-unity {
  /* We'll render the overlay on top of the Unity Application. */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: grey;
  /* We'll set the following Flex properties in order to center the text. */
  display: flex;
  justify-content: center;
  align-items: center;
}

.container-unity > .unity {
  /* The Unity Application matches it size to the container. */
  width: 100%;
  height: 100%;
}
