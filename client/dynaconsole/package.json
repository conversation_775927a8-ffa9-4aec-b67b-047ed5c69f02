{"name": "dynaconsole", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.0.15", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-query": "^5.69.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "framer-motion": "^12.6.2", "posthog-js": "^1.234.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^7.4.0", "react-unity-webgl": "^9.8.0", "tailwindcss": "^4.0.15"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}