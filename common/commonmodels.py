from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class RequestContext(BaseModel):
    userId: Optional[str] = Field(default=None)
    accountId: Optional[str] = Field(default=None)
    projectId: Optional[str] = Field(default=None)
    clientVersion: Optional[int] = Field(default=-1)
    sessionToken: Optional[str] = Field(default=None)
    tokenExpiry: Optional[datetime] = Field(default=None)
