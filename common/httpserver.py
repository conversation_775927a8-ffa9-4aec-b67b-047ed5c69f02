from datetime import datetime, timedelta
import io
import json
import os
import shutil
import subprocess
from typing import Annotated, List, Optional
import zipfile
from fastapi import (
    Body,
    FastAPI,
    Form,
    HTTPException,
    Request,
    File,
    Response,
    UploadFile,
    Depends,
)
from fastapi.responses import StreamingResponse
from fastapi import status
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from pydantic import BaseModel, Field, ValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.datastructures import MutableHeaders
import uvicorn
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
from app_dxf.DXFUtils import DXFData

IST_OFFSET = timedelta(hours=5, minutes=30)


class ModelSchema(BaseModel):
    model: str  # will be set


class EmailStr(BaseModel):
    email: str
    domain: str


class ComplexInput(BaseModel):
    name: str
    age: int
    email: EmailStr
    list_of_secondry_emails: list[EmailStr]
    dict_of_emails: dict[str, EmailStr]


class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Log request details
        print(f"\n=== Request ===  {request.method} {request.url.path}")
        print(f"Headers: {dict(request.headers)}")

        # Get and log body
        if False:
            body = await request.body()
            if body:
                try:
                    print(f"Body: {json.loads(body)}")
                except:
                    print(f"Body: {body.decode()}")

        response = await call_next(request)
        return response


secure_internal_resource = HTTPBasic()


def basic_auth_secure_internal_resource(
    credentials: Annotated[HTTPBasicCredentials, Depends(secure_internal_resource)],
):
    """Basic Auth Dependency"""
    correct_username = "mhxpinternal"
    correct_password = "0x7f6fb26e59f0"

    if (
        credentials.username != correct_username
        or credentials.password != correct_password
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials


def get_port_from_env() -> Optional[int]:
    """Get port from environment variable if set."""
    port_str = os.getenv("PORT")
    if port_str:
        try:
            return int(port_str)
        except ValueError:
            print(
                f"Warning: Invalid PORT environment variable value '{port_str}'. Using default port."
            )
    return 8000


class InputObj(BaseModel):
    name: str
    age: int


class DynamicCORSMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        # If it's a preflight request, create a response directly.
        if request.method == "OPTIONS":
            response = Response(status_code=200)
        else:
            response = await call_next(request)

        origin = request.headers.get("origin")
        if origin:
            headers = MutableHeaders(response.headers)
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
            headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            # Reflect requested headers or allow common ones.
            requested_headers = request.headers.get("Access-Control-Request-Headers")
            if requested_headers:
                headers["Access-Control-Allow-Headers"] = requested_headers
            else:
                headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type"
        return response


class FastAPIApp:
    ORIGINS_TO_ALLOW = [
        "http://localhost:5173",
        "https://localhost:5174",
        "https://d1gxeevpt5yjvu.cloudfront.net",
        "https://d369va5ul2i9x.cloudfront.net",
        "https://d110bmzq22urqb.cloudfront.net",
        "https://d1ya9vlu1s1w7t.cloudfront.net",
        "https://www.spaceviz.ai",
        "https://viz.spaceviz.ai",
        "www.spaceviz.ai",
    ]

    def __init__(self):
        self.app = FastAPI(docs_url=None, redoc_url=None, openapi_url=None)
        self.app.add_middleware(LoggingMiddleware)
        # """
        # may be later swith to more secure., couldnot use * for cookies and axios.
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=FastAPIApp.ORIGINS_TO_ALLOW,  # Allows all origins
            allow_credentials=True,
            allow_methods=["*"],  # Allows all methods
            allow_headers=["*"],  # Allows all headers
        )
        # """
        # self.app.add_middleware(DynamicCORSMiddleware)
        try:
            commit_info = (
                subprocess.check_output(
                    ["git", "log", "-1", "--format=%h %cd", "--date=iso"]
                )
                .strip()
                .decode("utf-8")
            )
            # Extract the commit timestamp and convert to datetime object
            commit_hash, commit_timestamp = commit_info.split(" ", 1)
            commit_time = datetime.fromisoformat(commit_timestamp)
            self.commit_info = (
                f"[{commit_hash}] @ {commit_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )
        except subprocess.CalledProcessError:
            commit_info = "cmd failed"
        self.startup_time = (datetime.now()).strftime("%Y-%m-%d %H:%M:%S")

        # Define routes
        @self.app.get("/", dependencies=[Depends(basic_auth_secure_internal_resource)])
        async def read_root():
            return {
                "message": f"dynaserver is running",
                "commit_info": self.commit_info,
                "startup_time": self.startup_time,
            }

        @self.app.post("/upload")
        async def upload_files(
            input_json: str = Form(...),
            files: Optional[List[UploadFile]] = File(default=None),
        ):
            """
            Endpoint to handle multiple file uploads and return a zip file.
            """
            saved_files = []
            zip_buffer = io.BytesIO()

            # Create a directory for saving uploaded files
            upload_dir = os.path.join(os.path.dirname(__file__), "uploads")
            os.makedirs(upload_dir, exist_ok=True)

            print(f"Received JSON: {input_json}")

            # Prepare the zip file in memory
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
                if files is not None:
                    for file in files:
                        file_path = os.path.join(upload_dir, file.filename)
                        print(f"Saving file {file.filename} to {file_path}")

                        # Save the file temporarily to disk
                        with open(file_path, "wb") as f:
                            shutil.copyfileobj(file.file, f)

                        saved_files.append(file.filename)

                        # Add the file to the zip
                        zip_file.write(file_path, file.filename)

                        # Optionally delete the file after adding to zip
                        os.remove(file_path)

                # Add the dummy JSON file to the zip
                dummy_json_content = {
                    "message": "This is a dummy JSON",
                    "received": input_json,
                }
                json_file_name = "response.json"
                zip_file.writestr(
                    json_file_name, json.dumps(dummy_json_content, indent=4)
                )

            zip_buffer.seek(0)  # Reset the buffer's pointer to the start

            # Return the zip file as a response
            return StreamingResponse(
                zip_buffer,
                media_type="application/zip",
                headers={
                    "Content-Disposition": "attachment; filename=uploaded_files.zip"
                },
            )

        @self.app.get("/schema/{class_name}")
        async def get_schema(class_name: str):
            clzes = {}
            clzes["DXFData"] = DXFData
            try:
                if class_name in clzes:
                    model_class = clzes[class_name]
                else:
                    model_class = globals()[class_name]
                if not issubclass(model_class, BaseModel):
                    raise HTTPException(
                        status_code=400, detail=f"{class_name} is not a Pydantic model"
                    )
                # Create wrapper with proper type annotation
                wrapper = type(
                    "ModelSchemaWrapper",
                    (BaseModel,),
                    {
                        "__annotations__": {"model": model_class},
                        "model": Field(default=None),
                    },
                )
                return model_class.model_json_schema()
            except KeyError:
                raise HTTPException(
                    status_code=404, detail=f"Class {class_name} not found"
                )

        @self.app.post(
            "/guru", dependencies=[Depends(basic_auth_secure_internal_resource)]
        )
        async def get_guru(name: str = Body(embed=False)):
            return {"guru": name}

        @self.app.post("/process_input")
        async def process_input(
            input_json: str = Form(...),  # Expecting the JSON of ComplexInput
            files: Optional[List[bytes]] = File(default=None),
        ) -> ComplexInput:
            input = None
            try:
                # Deserialize the JSON string into ComplexInput
                input = ComplexInput(**json.loads(input_json))
            except (json.JSONDecodeError, ValidationError) as e:
                raise HTTPException(status_code=422, detail="Invalid input JSON format")

            input.name = input.name + " processed"
            input.age = input.age + 1
            input.email.email = input.email.email + " processed"

            processed_files = []
            for file in files:
                file_content = await file.read()  # Read file content if needed
                processed_files.append(
                    {"filename": file.filename, "content_size": len(file_content)}
                )
                print(f"File {file.filename} processed with size {len(file_content)}")

            return input

        # Protect OpenAPI JSON schema
        @self.app.get(
            "/openapi.json", dependencies=[Depends(basic_auth_secure_internal_resource)]
        )
        async def get_open_api_endpoint():
            return get_openapi(
                title=self.app.title, version=self.app.version, routes=self.app.routes
            )

        # Protect Swagger UI (/docs)
        @self.app.get(
            "/docs", dependencies=[Depends(basic_auth_secure_internal_resource)]
        )
        async def get_swagger_ui():
            return get_swagger_ui_html(openapi_url="/openapi.json", title="Docs")

        # Protect ReDoc UI (/redoc)
        @self.app.get(
            "/redoc", dependencies=[Depends(basic_auth_secure_internal_resource)]
        )
        async def get_redoc_ui():
            return get_redoc_html(openapi_url="/openapi.json", title="ReDoc")

    def start(self):
        """Start the server."""
        port = get_port_from_env()
        print(f"Starting server on port {port}")
        uvicorn.run(self.app, host="0.0.0.0", port=port)


# Example usage
if __name__ == "__main__":
    app = FastAPIApp()

    print(json.dumps(ComplexInput.model_json_schema(), indent=2))

    @app.app.get("/anotherhttp/{name}")
    async def anotherhttp(name: str):
        return {"anotherhttp": name}

    dxfHttp = DXFHttp(app.app)

    app.start()
    print("Server started at ")
