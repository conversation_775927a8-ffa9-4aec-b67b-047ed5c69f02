import io
import os
import asyncio
from typing import BinaryIO
import aiofiles


class DiskFileCache:
    _instance: "DiskFileCache" = None

    @classmethod
    def getInstance(cls) -> "DiskFileCache":
        if cls._instance is None:
            cls._instance = DiskFileCache()
        return cls._instance

    def __init__(
        self,
    ):
        self.main_storage_path = os.getcwd() + "/FILE_STORAGE/"

    async def put_file(
        self, accountId: str, binary_io: BinaryIO, folder: str, filename: str
    ):
        if not accountId:
            accountId = "UNKNOWN_ACCOUNT"
        """Asynchronously writes a file from a binary stream."""
        full_path = os.path.join(self.main_storage_path, accountId, folder, filename)

        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        binary_io.seek(0)  # Reset stream position

        async with aiofiles.open(full_path, "wb") as f:
            await f.write(binary_io.read())

        print("Wrote:", full_path)
        await self.touch_file(full_path)

    async def touch_file(self, full_path: str):
        """Creates an access tracking file."""
        access_path = full_path + ".access"
        async with aiofiles.open(access_path, "wb") as f:
            await f.write(b"")
        print("Touched:", access_path)

    async def get_files_for_account(
        self, accountId: str, folder: str, file_type: str
    ) -> list[str]:
        full_path = os.path.join(self.main_storage_path, accountId, folder)
        if not os.path.exists(full_path):
            print("Folder not found:", full_path)
            return []

        # Get all files with the specified file type
        files = [
            f
            for f in os.listdir(full_path)
            if os.path.isfile(os.path.join(full_path, f)) and f.endswith(file_type)
        ]

        # Sort files by modification time (latest first)
        files.sort(
            key=lambda f: os.path.getmtime(os.path.join(full_path, f)), reverse=True
        )

        return files
    
    async def get_file(
        self, accountId: str, folder: str, filename: str
    ) -> io.BytesIO | None:
        # print("Getting file: " + accountId + " : " + folder + " : " + filename)
        if not accountId:
            accountId = "UNKNOWN_ACCOUNT"

        """Asynchronously reads a file into an in-memory stream."""
        full_path = os.path.join(self.main_storage_path, accountId, folder, filename)

        if not os.path.exists(full_path):
            print("File not found:", full_path)
            return None

        async with aiofiles.open(full_path, "rb") as f:
            content = await f.read()

        print("Read:", full_path)
        await self.touch_file(full_path)
        return io.BytesIO(content)


async def test():
    print("Testing Async File Storage")

    # Write file
    await DiskFileCache.getInstance().put_file(
        "testaccount2",
        io.BytesIO(b"Hello Async World"),
        "fo1/fold2",
        "test.txt",
    )

    # Read file
    file_content = await DiskFileCache.getInstance().get_file(
        "testaccount2",
        "fo1/fold2",
        "test.txt",
    )
    if file_content:
        print(await asyncio.to_thread(file_content.read))  # Read in a non-blocking way

    print("Tested")


if __name__ == "__main__":
    asyncio.run(test())
