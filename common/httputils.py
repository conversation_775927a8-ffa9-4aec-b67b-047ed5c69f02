import io
import json
import os
import shutil
from typing import Any, List
import zipfile

from fastapi.responses import StreamingResponse


def create_strean_response_json_file(obj: Any, files: List[Any]) -> StreamingResponse:

    zip_buffer = io.BytesIO()
    input_json = obj

    print(f"Received JSON: {input_json}")

    # Prepare the zip file in memory
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        if files is not None:
            for file in files:
                file_content = file.file.read()
                # Add the file to the zip (in-memory)
                zip_file.writestr(file.filename, file_content)
        # Add the dummy JSON file to the zip
        json_file_name = "response.json"
        zip_file.writestr(json_file_name, input_json.model_dump_json(indent=2))

    zip_buffer.seek(0)  # Reset the buffer's pointer to the start

    # Return the zip file as a response
    return StreamingResponse(
        zip_buffer,
        media_type="application/zip",
        headers={"Content-Disposition": "attachment; filename=uploaded_files.zip"},
    )
