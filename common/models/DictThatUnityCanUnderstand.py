from typing import List, TypeVar, Generic
from pydantic import BaseModel, Field

K = TypeVar('K')
V = TypeVar('V')

class KeyValuePair(BaseModel, Generic[K, V]):
    Key: K = Field()
    Value: V = Field()

class DictThatUnityCanUnderstand(BaseModel, Generic[K, V]):
    items: List[KeyValuePair[K, V]] = Field(default_factory=list)

    def put(self, key: K, value: V):
        # Check if key already exists
        for item in self.items:
            if item.Key == key:
                item.Value = value
                return
        # If key doesn't exist, add new pair
        self.items.append(KeyValuePair(Key=key, Value=value))

    def get(self, key: K) -> V:
        for item in self.items:
            if item.Key == key:
                return item.Value
        return None

    def try_get(self, key: K) -> V:
        for item in self.items:
            if item.Key == key:
                return item.Value
        return None

    def remove(self, key: K):
        self.items = [item for item in self.items if item.Key != key]

    def contains_key(self, key: K) -> bool:
        return any(item.Key == key for item in self.items)

    def clear(self):
        self.items.clear()

    @property
    def count(self) -> int:
        return len(self.items)

    def get_keys(self) -> List[K]:
        return [item.Key for item in self.items]

    def get_values(self) -> List[V]:
        return [item.Value for item in self.items]
