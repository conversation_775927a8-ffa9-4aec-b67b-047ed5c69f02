import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, func

from .db import Base


class Build_Status(Enum):
    MANUAL = "manual"
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Build(Base):
    __tablename__ = "builds"
    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(String, nullable=False, index=True)
    project_name = Column(String, nullable=False, index=True)
    build_number = Column(Integer, nullable=True, index=False)
    build_status = Column(String, nullable=False, index=False)
    build_url = Column(String, nullable=True, index=False)
    build_queue_id = Column(Integer, nullable=False, index=False)
    build_created_at = Column(
        DateTime, nullable=False, index=True, server_default=func.now()
    )
    requested_by = Column(String, nullable=True, index=False)
    bake_level = Column(Integer, nullable=True, index=False)
    furniture_toggle = Column(Integer, nullable=True, index=False)




class User(Base):
    __tablename__ = "users"
    email = Column(String, primary_key=True, index=True)
    password = Column(String, nullable=True, index=False)
    account_id = Column(String, nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, index=False, server_default=func.now())

    access_token = Column(String, nullable=True, index=False)
    token_expiry = Column(DateTime, nullable=True, index=False)