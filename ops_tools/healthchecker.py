import json
import os
import datetime
import boto3
import urllib.request
import urllib.error

# Environment Variables
S3_BUCKET = os.environ.get("S3_BUCKET", "mhxp.testbuckguru")
STATUS_KEY = os.environ.get("STATUS_KEY", "status_v1.json")
SLACK_WEBHOOK = os.environ.get("SLACK_WEBHOOK", "NA")  # Slack webhook URL

s3 = boto3.client("s3")

# Default configuration: List of endpoint definitions http://dynacloud.spaceviz.ai:8080/
DEFAULT_CONFIG = [
    {
        "endpoint_url": "https://dynacloud.spaceviz.ai/",
        "application": "Dynacloud Apps - Voice stuff",
        "expected_status_code": 401,
        "method": "GET",
        "check_interval_mins": 3,
    },
    {
        "endpoint_url": "https://api.spaceviz.ai/",
        "application": "SpaceViz App - Backend APIs",
        "expected_status_code": 401,
        "method": "GET",
        "check_interval_mins": 3,
    },
    {
        "endpoint_url": "http://dynacloud.spaceviz.ai:8080/",
        "application": "SpaceViz Backend File Browser UI",
        "expected_status_code": 200,
        "method": "GET",
        "check_interval_mins": 3,
    },
]

calling_context = None


def load_config():
    # For this example, simply return the DEFAULT_CONFIG.
    return DEFAULT_CONFIG


def load_status():
    """Load the current check status from S3.
    If not found, return an empty dictionary."""
    try:
        resp = s3.get_object(Bucket=S3_BUCKET, Key=STATUS_KEY)
        data = resp["Body"].read().decode("utf-8")
        status = json.loads(data)
    except Exception as e:
        print(
            f"Status file not found or error loading status: {e}. Initializing new status."
        )
        status = {}
        s3.put_object(Bucket=S3_BUCKET, Key=STATUS_KEY, Body=json.dumps(status))
    return status


def save_status(status):
    """Save the status (latest check per endpoint) to S3."""
    s3.put_object(Bucket=S3_BUCKET, Key=STATUS_KEY, Body=json.dumps(status, indent=2))


def send_slack_notification(
    message, status=None, url=None, error=None, duration=None, app_name=None
):
    """
    Send a nicely formatted Slack message using Block Kit attachments.
    - message: a header text.
    - status: "down" or "up" (used for color).
    - url: endpoint URL.
    - error: error message (if any).
    - duration: duration string for how long the endpoint has been down.
    - app_name: name of the application (if any).
    Returns True if sent successfully.
    """
    if not SLACK_WEBHOOK or SLACK_WEBHOOK == "NA":
        print("No Slack webhook configured.")
        return False

    # Set color based on status.
    if status == "down":
        color = "#ff0000"
    elif status == "up":
        color = "#36a64f"
    else:
        color = "#cccccc"

    # Build the attachment payload.
    blocks = [{"type": "section", "text": {"type": "mrkdwn", "text": f"*{message}*"}}]

    details = []
    if url:
        details.append(f"*Endpoint:* {url}")
    if app_name:
        details.append(f"*Application:* {app_name}")
    if error:
        details.append(f"*Error:* {error}")
    if duration:
        details.append(f"*Down for:* {duration}")
    if calling_context:
        details.append(f"*Source:* {calling_context.invoked_function_arn}")
    if details:
        blocks.append(
            {
                "type": "context",
                "elements": [{"type": "mrkdwn", "text": " \n | ".join(details)}],
            }
        )

    payload = {"attachments": [{"color": color, "blocks": blocks}]}

    data = json.dumps(payload).encode("utf-8")
    req = urllib.request.Request(
        SLACK_WEBHOOK, data=data, headers={"Content-Type": "application/json"}
    )
    try:
        with urllib.request.urlopen(req, timeout=10) as response:
            if response.getcode() == 200:
                return True
            else:
                print(f"Slack returned status code {response.getcode()}")
                return False
    except Exception as e:
        print(f"Error sending Slack notification: {e}")
        return False


def perform_http_request(url, method, timeout=10):
    """Perform an HTTP request using urllib.request.
    Returns (status_code, error_message) where error_message is None on success."""
    req = urllib.request.Request(url, method=method)
    try:
        with urllib.request.urlopen(req, timeout=timeout) as response:
            return response.getcode(), None
    except urllib.error.HTTPError as e:
        return e.code, None
    except Exception as e:
        return None, str(e)


def lambda_handler(event, context):
    global calling_context
    calling_context = context
    config = load_config()  # List of endpoint definitions
    status_data = load_status()  # A dict with each endpoint's latest record
    now = datetime.datetime.utcnow()
    updated = False

    for endpoint in config:
        url = endpoint["endpoint_url"]
        expected = endpoint["expected_status_code"]
        method = endpoint.get("method", "GET").upper()
        interval = endpoint.get("check_interval_mins", 5)
        alert_interval = endpoint.get("alert_repeat_interval_mins", interval)
        key = url  # Use the endpoint URL as key

        # Retrieve previous record (if any)
        prev = status_data.get(key, {})
        last_check_time = None
        if "timestamp" in prev:
            try:
                last_check_time = datetime.datetime.fromisoformat(prev["timestamp"])
            except Exception:
                last_check_time = None

        # Eligibility:
        # - If previous status is "down", then check on every invocation.
        # - Otherwise, check only if the interval has elapsed.
        eligible = False
        if prev.get("status") == "down":
            eligible = True
        else:
            if (
                last_check_time is None or (now - last_check_time).total_seconds() >= 58
            ):  # just check every minute, doesn't cost much.
                eligible = True
            else:
                print(
                    f"Skipping check for {url}: last check was at {last_check_time.isoformat()}"
                )
                continue

        # Prepare a new record with default fields.
        record = {
            "timestamp": now.isoformat(),
            "status": None,
            "response_code": None,
            "error_message": None,
            "alert_sent": False,
            "alert_reason": "",
            "last_alert_time": prev.get("last_alert_time"),
            "down_since": prev.get("down_since"),
        }

        if eligible:
            print(f"Checking {url}...")
            response_code, error_message = perform_http_request(url, method, timeout=10)
            if response_code is None:
                success = False
                record["error_message"] = error_message
            else:
                success = response_code == expected
            record["response_code"] = response_code
            new_status = "up" if success else "down"
            record["status"] = new_status

            alert_sent = False
            alert_reason = ""
            # If there is a previous record to compare...
            previous_status = "up"
            if prev is not None and prev.get("status") is not None:
                previous_status = prev["status"]

            if new_status != previous_status:
                # Status change
                if new_status == "down":
                    record["down_since"] = now.isoformat()
                    alert_msg = f"ALERT: Endpoint is DOWN!"
                    # No duration on state change
                    alert_sent = send_slack_notification(
                        alert_msg,
                        status="down",
                        url=url,
                        error=record.get("error_message", "N/A"),
                        app_name=endpoint.get("application"),
                    )
                else:
                    alert_msg = f"RECOVERY: Endpoint is UP again!"
                    record["down_since"] = None
                    alert_sent = send_slack_notification(
                        alert_msg,
                        status="up",
                        url=url,
                        app_name=endpoint.get("application"),
                    )
                alert_reason = f"Status changed from {previous_status} to {new_status}."
                record["last_alert_time"] = now.isoformat()
            else:
                if new_status == "down":
                    # Same state: still down
                    # Initialize down_since if missing
                    if not prev.get("down_since"):
                        record["down_since"] = prev.get("timestamp", now.isoformat())
                    # Calculate duration since it went down.
                    try:
                        down_since = datetime.datetime.fromisoformat(
                            record["down_since"]
                        )
                    except Exception:
                        down_since = now
                    duration = now - down_since
                    duration_str = str(duration).split(".")[0]
                    last_alert_time = None
                    if prev.get("last_alert_time"):
                        try:
                            last_alert_time = datetime.datetime.fromisoformat(
                                prev["last_alert_time"]
                            )
                        except Exception:
                            last_alert_time = None
                    if last_alert_time is None or (
                        now - last_alert_time
                    ).total_seconds() >= (alert_interval * 60):
                        alert_msg = (
                            f"REPEAT ALERT: Endpoint is still DOWN for {duration_str}!"
                        )
                        alert_sent = send_slack_notification(
                            alert_msg,
                            status="down",
                            url=url,
                            error=record.get("error_message", "N/A"),
                            duration=duration_str,
                            app_name=endpoint.get("application"),
                        )
                        alert_reason = f"Repeated alert: down for {duration_str} and alert interval elapsed."
                        record["last_alert_time"] = now.isoformat()
                    else:
                        alert_reason = "No new alert: down state persists but alert interval not elapsed."
                else:
                    alert_reason = "No alert: state is up and unchanged."

            record["alert_sent"] = alert_sent
            record["alert_reason"] = alert_reason

            status_data[key] = record
            updated = True

    if updated:
        save_status(status_data)

    return {"status": "completed", "data": status_data}


if __name__ == "__main__":
    # For local testing, simulate an empty event and a dummy context.
    class DummyContext:
        def __init__(self):
            self.function_name = "local_test"
            self.memory_limit_in_mb = 128
            self.invoked_function_arn = (
                "arn:aws:lambda:local:123456789012:function:local_test"
            )
            self.aws_request_id = "dummy-request-id"

    test_event = {}
    context = DummyContext()

    result = lambda_handler(test_event, context)
    print("Lambda Handler Result:")
    print(json.dumps(result, indent=2))
