import os
import boto3
import logging
from botocore.exceptions import ClientError
import sys
import time
from datetime import datetime
import mimetypes
import json
import argparse
import asyncio

from ops_tools.slacktools import SlackMessenger

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Environment Configuration Class
class DeploymentConfig:
    def __init__(
        self,
        bucket_name,
        cdn_endpoint,
        target_location,
        update_origin_path: bool = False,
        prefered_domain: str = None,
        index_page: str = "index.html",
        slacker: SlackMessenger = None,
        invalidation_wait_time: int = 400,
    ):
        self.bucket_name = bucket_name
        self.cdn_endpoint = cdn_endpoint
        self.target_location = target_location
        self.update_origin_path: bool = update_origin_path
        self.prefered_domain = prefered_domain
        self.index_page = index_page
        self.slacker = slacker
        self.invalidation_wait_time = invalidation_wait_time

    def __str__(self):
        return f"DeploymentConfig(bucket_name={self.bucket_name}, cdn_endpoint={self.cdn_endpoint}, target_location={self.target_location} update_origin_path={self.update_origin_path})"


# Map of environment to EnvConfig
env_config_map = {
    "prod_console": DeploymentConfig(
        "com.mhxp.dynaweb", "EQ0G7UDYW6I76", "prod/dynaconsole_builds", True
    ),
    "dev_console": DeploymentConfig(
        "com.mhxp.dynaweb", "EKXZKLF0UAY2", "dev/dynaconsole_builds", True
    ),
    "dev_console_2": DeploymentConfig(
        "com.mhxp.dynaweb", "EA1GW0CEA5UEA", "dev/dynaconsole_builds", True
    ),
    "dev": DeploymentConfig(
        "com.mhxp.dynaweb", "EKXZKLF0UAY2", "dev/dynatag_builds", True
    ),
    "prod": DeploymentConfig(
        "com.mhxp.dynaweb", "E24DG9V5P6SEPK", "prod/dynatag_builds", True
    ),
    "prod_viz": DeploymentConfig(
        "com.mhxp.dynaweb",
        "E1BW24L7ZTD00D",
        "prod/viz_builds/ACCOUNT_NAME/PROJECT_NAME",
        False,
        prefered_domain="https://viz.spaceviz.ai",
        index_page="index.html",
        # slacker=SlackMessenger("*********************************************************************************")
        slacker=SlackMessenger(
            # "*********************************************************************************"
            "*********************************************************************************"
        ),
        invalidation_wait_time=0,  # don't wait
    ),
}


class Deployment:
    def __init__(
        self,
        env_name: str,
        account_name: str = None,
        project_name: str = None,
        build_number: str = None,
        triggered_by: str = None,
        bake_levle: int = -1,
        furniture_toggle: int = -1,
    ):

        self.depConfig: DeploymentConfig = env_config_map.get(env_name)
        # Configuration
        self.build_start_time = datetime.now()
        self.timestamp = self.build_start_time.strftime("%y%m%d%H%M%S")
        self.timestamp_human = self.build_start_time.strftime("%Y-%m-%d %H:%M:%S")
        self.build_folder_name = (
            build_number
            if build_number
            else ("build_" + self.build_start_time.strftime("%Y_%m_%d__%H_%M_%S"))
        )
        self.triggered_by = triggered_by or os.getenv("USER") or os.getenv("USERNAME")
        self.build_details = {
            "build": self.timestamp,
            "build_human": self.timestamp_human,
            "build_folder": build_number if build_number else self.build_folder_name,
            "machine": os.uname().nodename,
            "user": self.triggered_by,
            "env": env_name,
        }
        self.build_number = build_number
        self.account_name = account_name
        self.project_name = project_name
        self.build_details["account_name"] = account_name
        self.build_details["project_name"] = project_name
        self.s3_target_path_deployed = None
        self.bake_level = bake_levle
        self.furniture_toggle = furniture_toggle

    async def deploy(self, local_folder_path: str):
        logging.info(
            f"Deploying {local_folder_path} to {self.depConfig} : build : {self.timestamp}"
        )
        self.update_s3(local_folder_path)
        await self.update_cdn()
        self.send_slack_notif()

    def send_slack_notif(self):

        if not self.depConfig.slacker:
            print("Slack not configured skip")
            return
        slack_attachment = {
            "color": "#36a64f",
            "fields": [
                {
                    "title": "Build Details",
                    "value": f"*Account:* {self.account_name} \n*Project:* {self.project_name} \n*Build:* {self.build_number} \n *By:* {self.triggered_by}",  # Use Python variable instead of shell variable
                    "short": True,
                },
                {
                    "title": "Build URL 🚀 ",
                    "value": f"{self.full_cdn_url}",  # Use Python f-string
                    "short": False,
                },
                {
                    "title": "Build Params ",
                    "value": f"bake_level : {self.bake_level} \nfurniture_toggle : {self.furniture_toggle}",  # Use Python f-string
                    "short": False,
                },
            ],
        }
        self.depConfig.slacker.send_message(
            f"🏠 *Your Home Is Built! *", [slack_attachment]
        )
        print("Messsage sent!")

    # Function to upload a folder to S3
    def update_s3(self, local_folder_path: str):

        s3_client = boto3.client("s3")
        self.s3_target_path_deployed = os.path.join(
            self.depConfig.target_location, f"{self.build_folder_name}"
        )
        if self.account_name:
            self.s3_target_path_deployed = self.s3_target_path_deployed.replace(
                "ACCOUNT_NAME", self.account_name
            )
        if self.project_name:
            self.s3_target_path_deployed = self.s3_target_path_deployed.replace(
                "PROJECT_NAME", self.project_name
            )
        logger.info(f"Target location: {self.s3_target_path_deployed}")

        # Convert build details to JSON
        self.build_details["local_folder_path"] = local_folder_path

        build_file_path = os.path.join(local_folder_path, "build.json")
        with open(build_file_path, "w") as build_file:
            json.dump(self.build_details, build_file, indent=4)

        logging.info(f"Build details : {json.dumps(self.build_details, indent=4)}")

        for root, dirs, files in os.walk(local_folder_path):

            for file in files:
                ExtraArgs = {}
                if file == "build.json":
                    logger.info(f"Uploading build.json")

                mime_type, _ = mimetypes.guess_type(file)
                if mime_type:
                    ExtraArgs["ContentType"] = mime_type
                    logger.info(f"Mime type attached to file {file} : {mime_type}")

                # Explicitly set Content-Encoding for Brotli compressed files
                if file.endswith(".br"):
                    ExtraArgs["ContentEncoding"] = "br"
                    print(f"Setting Content-Encoding: br for {file}")

                file_path = os.path.join(root, file)
                s3_key = os.path.join(
                    self.s3_target_path_deployed,
                    os.path.relpath(file_path, local_folder_path),
                )
                try:
                    s3_client.upload_file(
                        file_path, self.depConfig.bucket_name, s3_key, ExtraArgs
                    )
                    logger.info(
                        f"Uploaded {file_path} to s3://{self.depConfig.bucket_name}/{s3_key}"
                    )
                except ClientError as e:
                    logger.error(f"Failed to upload {file_path}: {e}")
        logger.info(f"\n -- S3 target path deployed: {self.s3_target_path_deployed}")

    # Function to invalidate the CDN using Boto3
    async def update_cdn(self):

        logger.info(f"\nUpdaing CDN: {self.depConfig}")
        distribution_id = self.depConfig.cdn_endpoint
        cloudfront_client = boto3.client("cloudfront")
        try:
            response = cloudfront_client.get_distribution_config(Id=distribution_id)
            config = response["DistributionConfig"]
            origin_id = config["Origins"]["Items"][0]["Id"]  # Get first origin ID
            old_origin_path = config["Origins"]["Items"][0]["OriginPath"]
            origin_path = old_origin_path  # unless changed.
            if self.depConfig.update_origin_path:

                etag = response["ETag"]  # Required for update

                # Step 2: Modify the Origin Path (Assuming the first origin is the S3 bucket)

                origin_path = "/" + self.s3_target_path_deployed

                config["Origins"]["Items"][0][
                    "OriginPath"
                ] = origin_path  # Update to new folder

                # Step 3: Update the distribution
                update_response = cloudfront_client.update_distribution(
                    Id=distribution_id,
                    DistributionConfig=config,
                    IfMatch=etag,  # Required to avoid conflicts
                )
                logger.info(
                    f"Updated origin id : {origin_id} path to {origin_path} , old : {old_origin_path} = {update_response}"
                )
            else:
                logger.info(f"Skipping origin path update for {distribution_id}")

            invalidation = cloudfront_client.create_invalidation(
                DistributionId=distribution_id,
                InvalidationBatch={
                    "Paths": {"Quantity": 1, "Items": ["/*"]},
                    "CallerReference": str(time.time()),
                },
            )
            logger.info(
                f"CDN invalidation created: {invalidation['Invalidation']['Id']}"
            )
            # Log the CDN endpoint HTTP URL using the Distribution Domain Name
            distribution = cloudfront_client.get_distribution(Id=distribution_id)
            domain_name = distribution["Distribution"]["DomainName"]
            cdn_url = f"https://{domain_name}"
            if self.depConfig.prefered_domain:
                cdn_url = self.depConfig.prefered_domain
            url_context = ("/" + self.s3_target_path_deployed).removeprefix(origin_path)
            print("url context " + url_context + " :: origin " + origin_path)
            full_cdn_url = f"{cdn_url}{url_context}/{self.depConfig.index_page}"
            logger.info(f"CDN Endpoint URL: {cdn_url} {full_cdn_url}")

            # Log the alternate domain names (CNAMEs)
            alternate_domains = config.get("Aliases", {}).get("Items", [])
            if alternate_domains:
                logger.info("Alternate Domains (CNAMEs):")
                for domain in alternate_domains:
                    logger.info(domain)
            else:
                logger.info("No alternate domains.")

            # Wait for the invalidation to complete
            invalidation_id = invalidation["Invalidation"]["Id"]
            wt_per_loop = 5
            total_wt = 0
            if self.depConfig.invalidation_wait_time > 0:
                while True:
                    if total_wt > self.depConfig.invalidation_wait_time:
                        logger.error(
                            f"CDN invalidation {self.depConfig.cdn_endpoint} {invalidation_id} timed out after {self.depConfig.invalidation_wait_time} seconds."
                        )
                        break
                    invalidation_status = cloudfront_client.get_invalidation(
                        DistributionId=distribution_id, Id=invalidation_id
                    )["Invalidation"]["Status"]
                    if invalidation_status == "Completed":
                        logger.info(f"CDN invalidation {invalidation_id} completed.")
                        break
                    logger.info(
                        f"Waiting for CDN invalidation {invalidation_id} to complete... [{invalidation_status}] ... {full_cdn_url}"
                    )
                    await asyncio.sleep(
                        wt_per_loop
                    )  # Wait for 5 seconds before checking again
                    total_wt += wt_per_loop
            else:
                logger.info(
                    f"Skipping wait for CDN invalidation {self.depConfig.cdn_endpoint} {invalidation_id} as wait time is set to 0."
                )

            logger.info(f"Finished CDN Endpoint URL: {cdn_url}")
            self.full_cdn_url = full_cdn_url
            self.url_context = url_context
        except ClientError as e:
            logger.error(f"Failed to invalidate CDN: {e}", e)
            raise e


# Example usage


# Main function to handle command line arguments
async def main():

    parser = argparse.ArgumentParser(description="Deploy to AWS S3 and invalidate CDN.")
    parser.add_argument(
        "-f", "--folder", required=True, help="Path to the local folder to deploy"
    )
    parser.add_argument(
        "-e", "--env", required=True, help="Deployment environment (dev, prod, etc.)"
    )
    parser.add_argument(
        "-a", "--account", required=False, help="Account name (optional)"
    )
    parser.add_argument(
        "-p", "--project", required=False, help="Project name (optional)"
    )
    parser.add_argument("-b", "--build_number", required=False, help="Build Number")
    args = parser.parse_args()

    local_folder_path = args.folder
    env = args.env
    account_name = args.account
    project = args.project
    build_number = args.build_number
    deployment = Deployment(
        env, account_name=account_name, project_name=project, build_number=build_number
    )
    await deployment.deploy(local_folder_path)
    print("Finished. " + deployment.full_cdn_url + " :: " + deployment.url_context)


if __name__ == "__main__":
    print("Starting deployment script")
    asyncio.run(main())
    print("Deployment script finished")
