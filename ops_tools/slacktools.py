import requests


class SlackMessenger:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url

    def send_message(self, message, attachments=None):
        payload = {"text": message}

        if attachments:
            payload["attachments"] = attachments

        response = requests.post(self.webhook_url, json=payload)

        if response.status_code != 200:
            raise ValueError(
                f"Request to <PERSON><PERSON>ck returned an error {response.status_code}, the response is:\n{response.text}"
            )
