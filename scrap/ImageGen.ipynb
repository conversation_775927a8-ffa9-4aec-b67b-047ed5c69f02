{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from google import genai\n", "from google.genai import types\n", "from PIL import Image\n", "from io import BytesIO\n", "\n", "import PIL.Image\n", "\n", "image = PIL.Image.open(\"/Users/<USER>/Downloads/kitchen3.png\")\n", "\n", "\n", "client = genai.Client(api_key=\"AIzaSyBKvXjiciwV8UlOMiUNawoRZaRZY7xjbFY\") #<EMAIL>\n", "\n", "text_input = (\"This is a picture of a kitchen.\" \"Can you style it like a modern kitchen, it should have minimalistic design\",)\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash-exp-image-generation\",\n", "    contents=[text_input, image],\n", "    config=types.GenerateContentConfig(response_modalities=[\"Text\", \"Image\"]),\n", ")\n", "\n", "for part in response.candidates[0].content.parts:\n", "    if part.text is not None:\n", "        print(part.text)\n", "    elif part.inline_data is not None:\n", "        image = Image.open(BytesIO(part.inline_data.data))\n", "        image.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}