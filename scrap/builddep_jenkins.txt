pipeline {
    agent any
    parameters {
        string(
            name: 'UNITY_PROJECT_PATH',
            defaultValue: 'C:\\Users\\<USER>\\Documents\\UnityProjects\\DynaTag',
            description: 'Full path to the Unity project directory'
        )
        string(
            name: 'UNITY_EDITOR_PATH',
            defaultValue: 'C:/Program Files/Unity/Hub/Editor/2023.2.20f1/Editor/Unity.exe',
            description: 'Full path to Unity editor executable'
        )
        choice(
            name: 'BUILD_TARGET',
            choices: ['WebGL', 'StandaloneWindows64', 'StandaloneOSX', 'Android'],
            description: 'Target platform for the build'
        )
        string(name: 'DYNASERVER', defaultValue: 'https://api.spaceviz.ai', description: 'Base domain for dynaserver')
        string(name: 'QUEUE_ID', defaultValue: '1', description: 'Queue ID that this job instance owns.')

    }
    
    environment {
        UNITY_LOG_PATH = "${params.UNITY_PROJECT_PATH}/unity_log.txt"
        BUILD_ZIP_NAME = "${params.BUILD_TARGET}.zip"
    }
    
    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    env.DSAPI="https://dominant-werewolf-proper.ngrok-free.app"
                    // Check if paths exist
                    if (!fileExists(params.UNITY_EDITOR_PATH)) {
                        error "Unity editor not found at specified path: ${params.UNITY_EDITOR_PATH}"
                    }
                    if (!fileExists(params.UNITY_PROJECT_PATH)) {
                        error "Unity project not found at specified path: ${params.UNITY_PROJECT_PATH}"
                    }
                }
            }
        }
       stage('Deque the next build request') {
            steps {
                script {
                    echo "Starting the build request process..."
        
                    print "Waiting for a build in the queue."
                    while (true) {
        
                        bat 'curl -s -X GET -w "%%{http_code}" -o uploaded_files.zip %DYNASERVER%/project/build/inqueue/%QUEUE_ID% > status.txt'
                       
                        bat 'type status.txt'
                        def responseCode = readFile('status.txt').trim().replaceAll("\\D+", "") // Remove unwanted text
                        echo "HTTP Response Code: ${responseCode}"
                        if (responseCode == "200") {
                            echo "Build found in queue!"
                            break
                        }else{
                            sleep 20  // Wait 30 seconds before retrying
                            print "."
                        }
                    }
                    echo "!"
                    echo "Build found for processing .."
                  
                    // Step 2: List files before extraction
                    bat 'dir'
        
                    // Step 3: Extract the zip file
                    bat 'tar -xf uploaded_files.zip'
                    
                    // Step 4: List files after extraction
                    bat 'dir'
                    echo "reading json .."
                    // Step 5: Read response.json from the extracted directory
                    def jsonText = readFile('response.json')
                    try {
                        echo "Raw JSON content: ${jsonText}"  // <-- Print JSON content
                        def json = readJSON text: jsonText
            
                        // Step 6: Extract build_id and filename
                        def build_id = json.props.build_id
                        def filename = json.props.filename
                        def acc_id = json.props.account_id
                        env.BUILD_ID = build_id
                        env.MHX_FILE = filename
                        env.ACCOUNT_ID = acc_id 
                        // Print extracted values
                        echo "Build ID: ${build_id} ${env.BUILD_ID}"
                        echo "Filename: ${filename} ${env.MHX_FILE} = ${env.ACCOUNT_ID}"
                        bat "curl -X POST  %DYNASERVER%/project/build/status/%BUILD_ID%/processing"
                        echo "marked processing"
            
                        // Step 7: Verify the extracted file
                        if (fileExists(filename)) {
                            echo "File ${filename} extracted successfully!"
                        } else {
                            echo "File ${filename} not found after extraction!"
                            error "Extraction failed or file missing!"
                        }
                    } catch (Exception e) {
                        echo "JSON Parsing Failed: ${e.message}"
                        error "Stopping pipeline due to JSON error!"
                    }
                  
                }
            }
        }

    
        stage('Create MHX Scene and Build') {
            steps {
                timeout(time: 20, unit: 'MINUTES') {
                    bat """
                    "${params.UNITY_EDITOR_PATH}" -batchmode -logFile "${UNITY_LOG_PATH}" -projectPath "${params.UNITY_PROJECT_PATH}" -executeMethod SceneCreator.CreateMHXSceneAndBuild -buildTarget ${params.BUILD_TARGET} -mhxfile "${env.MHX_FILE}"
                    """
                }
            }
        }
        
        stage('Cleanup') {
            steps {
                bat """
                    if exist "${params.UNITY_PROJECT_PATH}\\Temp" rmdir /s /q "${params.UNITY_PROJECT_PATH}\\Temp"
                    if exist "${params.UNITY_PROJECT_PATH}\\Builds\\Temp" rmdir /s /q "${params.UNITY_PROJECT_PATH}\\Builds\\Temp"
                """
            }
        }
        
        stage('Copy Build to Workspace') {
            steps {
                bat """
                    if not exist "Builds" mkdir "Builds"
                    copy "${params.UNITY_PROJECT_PATH}\\Builds\\${BUILD_ZIP_NAME}" "Builds\\${BUILD_ZIP_NAME}"
                """
            }
        }
        
        stage('Deploy Build to SpaceViz') {
            steps {
                bat """
                    for %%F in ("%MHX_FILE%") do set PROJECT_NAME=%%~nxF
                    
                    rem Use PowerShell to URL-encode PROJECT_NAME
        
                    rem for /f %%A in ('powershell -NoProfile -Command "[System.Net.WebUtility]::UrlEncode('%PROJECT_NAME%').Replace('+', '%20')"') do set PROJECT_NAME_ENCODED=%%A
                    for /f %%A in ('powershell -NoProfile -Command "[System.Uri]::EscapeDataString('%PROJECT_NAME%')"') do set PROJECT_NAME_ENCODED=%%A


                    echo "Deploying : ${params.UNITY_PROJECT_PATH}\\Builds\\${BUILD_ZIP_NAME} for %PROJECT_NAME% ; %ACCOUNT_NAME%" 
                    
                    
                    curl -X POST "${params.DYNASERVER}/project/build_deploy/${env.ACCOUNT_ID}/%PROJECT_NAME_ENCODED%/${env.BUILD_ID}/%BUILD_NUMBER%" -H "accept: application/json" -H "header-request-context: {}" -H "Content-Type: multipart/form-data" -F "input_json=string" -F "files=@${params.UNITY_PROJECT_PATH}\\Builds\\${BUILD_ZIP_NAME};type=application/zip"

                """
            }
            
        }
        
        
        stage('Archive Build') {
            steps {
                archiveArtifacts artifacts: "Builds/${BUILD_ZIP_NAME}", fingerprint: true
            }
        }
        
        
    }
    
    post {
        always {
            cleanWs()
        }
        failure {
            script {
                if (fileExists(UNITY_LOG_PATH)) {
                    echo "Build failed. Unity log can be found at: ${UNITY_LOG_PATH}"
                }
            }
            
            slackSend (
            channel: '#guru-test',  // Replace with your Slack channel
            color: 'danger',
            message: "*Build FAILED* :cry:\nProject: `${params.UNITY_PROJECT_PATH}`\nTarget: `${params.BUILD_TARGET}`\nCheck logs in Jenkins."
        )
        }
    }
}